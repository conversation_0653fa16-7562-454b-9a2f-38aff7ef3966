"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { HiOutlineHome } from "react-icons/hi2";
import { BsChatLeftDots } from "react-icons/bs";
import { FiInfo } from "react-icons/fi";
import { LuPhoneCall } from "react-icons/lu";
import { LiaCarSideSolid } from "react-icons/lia";
import { PiHeadset } from "react-icons/pi";


const PassangerWebFooter = () => {
  const [activeTab, setActiveTab] = useState("Schedule");
  const router = useRouter();

  const tabs = [
    { name: "Home", icon: <HiOutlineHome size={30} />, path: "/passanger/dashboard" },
    { name: " My Rides", icon: <LiaCarSideSolid size={30} />, path: "/passanger/my-rides" },
    { name: "Chat", icon: <BsChatLeftDots size={30} />, path: "/passanger/chat" },
    { name: "Help", icon: <PiHeadset size={30} />, path: "/passanger/help" },
  ];

  const handleTabClick = (tab) => {
    setActiveTab(tab.name);
    router.push(tab.path); // 👉 Navigate to path
  };

  return (
    <div className="z-999 block sm:hidden fixed bottom-0 w-full bg-white shadow-[0_4px_20px_rgba(0,0,0,0.1)] flex justify-around items-center py-3 rounded-t-[30px] z-50">
      {tabs.map((tab) => (
        <button
          key={tab.name}
          onClick={() => handleTabClick(tab)}
          className="flex flex-col items-center text-sm"
        >
          <div
            className={`text-2xl ${
              activeTab === tab.name ? "text-purple" : "text-[#76787A]"
            }`}
          >
            {tab.icon}
          </div>
          <span
            className={`mt-1 ${
              activeTab === tab.name ? "text-purple font-medium" : "text-[#76787A]"
            }`}
          >
            {tab.name}
          </span>
        </button>
      ))}
      {/* <div className="absolute top-[-5rem] right-4">
    <button className="bg-[#FF5F5F] text-white p-3 rounded-full shadow-lg">
    <LuPhoneCall />
    </button>
    </div> */}
    </div>
  );
};

export default PassangerWebFooter;
