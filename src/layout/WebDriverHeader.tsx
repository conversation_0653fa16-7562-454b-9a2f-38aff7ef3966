"use client";

import UserDropdown from "@/components/header/UserDropdown";
import { useSidebar } from "@/context/SidebarContext";
import Image from "next/image";
import Link from "next/link";
import React, { useState, useEffect, useRef } from "react";
import { IoIosSearch } from "react-icons/io";
import { usePathname } from "next/navigation";
import { useHeaderTitle } from "@/context/HeaderTitleContext";
import { TbHelpTriangle, TbMenuDeep, TbDashboard, TbCalendarDue} from "react-icons/tb";
import { FiInfo, FiPhoneCall } from "react-icons/fi";
import { IoCheckmarkDoneOutline } from "react-icons/io5";
import { LiaCarSideSolid } from "react-icons/lia";
import { LuWallet, LuCalendarCheck2 } from "react-icons/lu";
import { HiOutlineBell } from "react-icons/hi";
import { BsChatLeftDots } from "react-icons/bs";




const WebDriverHeader: React.FC = () => {
  const [isApplicationMenuOpen, setApplicationMenuOpen] = useState(false);
  const { toggleSidebar, toggleMobileSidebar } = useSidebar();
  const pathname = usePathname();
  const [routeName, setRouteName] = useState("");
  const [availability, setAvailability] = useState<"available" | "break" | "unavailable">("available");
  const { title } = useHeaderTitle();
  const [showNewNotifications, setShowNewNotifications] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
// mobile-header-start
const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
const [showMobileHeader, setShowMobileHeader] = useState(true);
const [isMobileSearchOpen, setIsMobileSearchOpen] = useState(false);

useEffect(() => {
  const handleResize = () => {
    if (window.innerWidth >= 1024) {
      setIsMobileMenuOpen(false);
    }
  };
  window.addEventListener("resize", handleResize);
  return () => window.removeEventListener("resize", handleResize);
}, []);

// mobile-header-end 
  const handleToggle = () => {
    if (window.innerWidth >= 1024) {
      toggleSidebar();
    } else {
      toggleMobileSidebar();
    }
  };

  const toggleApplicationMenu = () => {
    setApplicationMenuOpen(!isApplicationMenuOpen);
  };

  const inputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if ((event.metaKey || event.ctrlKey) && event.key === "k") {
        event.preventDefault();
        inputRef.current?.focus();
      }
    };
    document.addEventListener("keydown", handleKeyDown);
    return () => {
      document.removeEventListener("keydown", handleKeyDown);
    };
  }, []);

  useEffect(() => {
    if (pathname) {
      const formattedRoute = pathname
        .replace("/", "")
        .replace(/-/g, " ")
        .replace(/\b\w/g, (char) => char.toUpperCase());
      setRouteName(formattedRoute || "");
    }
  }, [pathname]);

  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setShowNewNotifications(false);
      }
    }
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  return (
    <header className="sticky top-0 w-full bg-white border-gray-200 z-999 dark:border-gray-800 dark:bg-gray-900 lg:border-b" style={{
      background: 'linear-gradient(195deg, #7018EB 14.85%, #3707EF 37.49%, #258ABB 69.91%, #18EC95 116.49%)',
    }}>
      <div className="bg-white flex flex-col items-center justify-between grow lg:flex-row lg:px-6 hidden sm:block rounded-tl-[20px]">
        <div className="flex justify-between items-center w-full gap-2 px-3 py-2 border-b border-gray-200 dark:border-gray-800 sm:gap-4 
        lg:border-b-0 lg:py-2">
          <Link href="/" className="lg:hidden">
            <Image width={154} height={32} className="dark:hidden" src="/images/sidebar/logo.png" alt="Logo" />
            <Image width={154} height={32} className="hidden dark:block" src="./images/logo/logo-dark.svg" alt="Logo" />
          </Link>

          <button onClick={toggleApplicationMenu} className="flex items-center justify-center w-10 h-10 text-gray-700 rounded-lg z-99999 hover:bg-gray-100 dark:text-gray-400 dark:hover:bg-gray-800 lg:hidden">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path fillRule="evenodd" clipRule="evenodd" d="M5.99902 10.4951C6.82745 10.4951 7.49902 11.1667 7.49902 11.9951V12.0051C7.49902 12.8335 6.82745 13.5051 5.99902 13.5051C5.1706 13.5051 4.49902 12.8335 4.49902 12.0051V11.9951C4.49902 11.1667 5.1706 10.4951 5.99902 10.4951ZM17.999 10.4951C18.8275 10.4951 19.499 11.1667 19.499 11.9951V12.0051C19.499 12.8335 18.8275 13.5051 17.999 13.5051C17.1706 13.5051 16.499 12.8335 16.499 12.0051V11.9951C16.499 11.1667 17.1706 10.4951 17.999 10.4951ZM13.499 11.9951C13.499 11.1667 12.8275 10.4951 11.999 10.4951C11.1706 10.4951 10.499 11.1667 10.499 11.9951V12.0051C10.499 12.8335 11.1706 13.5051 11.999 13.5051C12.8275 13.5051 13.499 12.8335 13.499 12.0051V11.9951Z" fill="currentColor" />
            </svg>
          </button>

          <div className="hidden lg:block">
            <form>
              <div className="relative items-center justify-between flex gap-[15px] w-full">
                <p className="text-[#050013] text-[17px] font-normal dark:text-gray-300">{title}</p>
                <div className="relative">
                  <span className="absolute left-2 top-1/2 -translate-y-1/2 pointer-events-none bg-cstm-grey p-[10px] rounded-[50px]">
                    <svg className="fill-gray-500 dark:fill-gray-400" width="20" height="20" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                      <path fillRule="evenodd" clipRule="evenodd" d="M3.04175 9.37363C3.04175 5.87693 5.87711 3.04199 9.37508 3.04199C12.8731 3.04199 15.7084 5.87693 15.7084 9.37363C15.7084 12.8703 12.8731 15.7053 9.37508 15.7053C5.87711 15.7053 3.04175 12.8703 3.04175 9.37363ZM9.37508 1.54199C5.04902 1.54199 1.54175 5.04817 1.54175 9.37363C1.54175 13.6991 5.04902 17.2053 9.37508 17.2053C11.2674 17.2053 13.003 16.5344 14.357 15.4176L17.177 18.238C17.4699 18.5309 17.9448 18.5309 18.2377 18.238C18.5306 17.9451 18.5306 17.4703 18.2377 17.1774L15.418 14.3573C16.5365 13.0033 17.2084 11.2669 17.2084 9.37363C17.2084 5.04817 13.7011 1.54199 9.37508 1.54199Z" fill="" />
                    </svg>
                  </span>
                  <input type="text" name="search" aria-label="search" className="dark:bg-dark-900 h-11 w-full rounded-lg bg-transparent border-0 py-3 pl-8 pr-14 text-sm text-gray-800 placeholder:text-gray-400 focus:border-red-500 focus:shadow-none border border-gray-300 px-3" ref={inputRef} />
                </div>
              </div>
            </form>
          </div>

         

          <div className="hidden lg:flex items-center">
            {/* start  */}
            <div className="flex items-center gap-4 relative">
            <div className="flex items-center bg-tables dark:bg-gray-800 rounded-full p-1">
              {["available", "break", "unavailable"].map((opt) => {
                const labels = {
                  available: "Available",
                  break: "Break",
                  unavailable: "Unavailable",
                };
                const active = opt === availability;
                return (
                  <button
                    key={opt}
                    onClick={() => setAvailability(opt)}
                    className={`px-6 py-1.5 text-[12px] font-normal rounded-full transition-colors duration-200  ${active
                      ? opt === "available"
                        ? "bg-[linear-gradient(183deg,_#079F61_7.15%,_#18EC95_96.74%)] text-white"
                        : opt === "break"
                          ? "bg-[linear-gradient(284.14deg,_#C78C35_3.57%,_#F79E18_106.42%)] text-white"
                          : "bg-[linear-gradient(284.14deg,_#C83434_3.57%,_#FB6565_106.42%)] text-white"
                      : "text-[#76787A] dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700"
                      }`}
                  >
                    {labels[opt]}
                  </button>
                );
              })}
            </div>

            <button className="flex items-center gap-2 text-[13px] font-medium px-3 py-1.5 rounded-full border-btn text-btn hover:bg-gray-100 dark:border-gray-600 dark:text-white dark:hover:bg-gray-800" onClick={() => alert("Issue reporting clicked")}>
              <TbHelpTriangle className="text-lg" /> Report an Issue
            </button>
          </div>
            {/* end  */}
          {/* start  */}
          <div className="relative mx-3" ref={dropdownRef}>
              <button onClick={() => setShowNewNotifications(!showNewNotifications)} className="relative dropdown-toggle flex items-center justify-center text-gray-500 transition-colors bg-white rounded-full hover:text-gray-700 h-11 w-11 hover:bg-gray-100 dark:border-gray-800 bg-cstm-grey dark:bg-gray-900 dark:text-gray-400 dark:hover:bg-gray-800 dark:hover:text-white text-[20px]">
                <HiOutlineBell />
              </button>
              {showNewNotifications && (
                <div className="absolute right-0 mt-2 w-[380px] bg-white rounded-lg shadow-xl z-50">
                  <div className="absolute -top-2 right-6 w-4 h-4 bg-tables transform rotate-45"></div>
                  <div className="px-4 py-3 flex justify-between items-center bg-tables">
                    <h4 className="text-[16px] font-normal text-[#050013]">Notifications</h4>
                    <span className="text-[13px] text-btn cursor-pointer flex items-center"><IoCheckmarkDoneOutline className="pr-2 text-[24px] text-[#76787A]" /> Mark as all read</span>
                  </div>
                  <div className="max-h-[400px] overflow-y-auto custom-scrollbar">
                    <div className="p-4 border-b">
                      <p className="text-[12px] text-[#76787A] mb-2">Today</p>
                      <div className="flex items-start gap-3">
                        <div className="p-2 rounded-[20px] bg-purple-100 flex items-center justify-center">
                          <span className="text-purple-600 text-lg"><LiaCarSideSolid />
                          </span>
                        </div>
                        <div className="text-sm">
                          <p className="font-semibold text-[#050013]">New Ride Request</p>
                          <p className="text-[13px] text-[#050013]">From: Spuistraat City to Schipol Airport</p>
                          <button className="mt-2 px-4 py-1 rounded-full bg-cstm-blue-700 text-white text-[12px]">View</button>
                        </div>
                        <span className="ml-auto text-xs text-[#76787A]">15 mins ago</span>
                      </div>
                    </div>
                    <div className="p-4 border-b">
                      <div className="flex items-start gap-3">
                        <div className="p-2 rounded-[20px] bg-green-100 flex items-center justify-center">
                          <span className="text-green-600 text-lg"><LuWallet /></span>
                        </div>
                        <div className="text-sm">
                          <p className="font-semibold text-[#050013]">Payment Received</p>
                          <span className="flex">
                            <p className="text-btn text-[13px] pr-1">$24.50</p>
                            <p className="text-[13px] text-[#050013]">received for trip #2847</p>
                          </span>
                        </div>
                        <span className="ml-auto text-xs text-[#76787A]">1 hr ago</span>
                      </div>
                    </div>
                    <div className="p-4 border-b">
                      <p className="text-[12px] text-[#76787A] mb-2">Earlier</p>
                      <div className="flex items-start gap-3">
                        <div className="p-2 rounded-[20px] bg-pink-100 flex items-center justify-center">
                          <span className="text-pink-600 text-lg"><HiOutlineBell />
                          </span>
                        </div>
                        <div className="text-sm">
                          <p className="font-semibold text-[#050013]">Policy Update</p>
                          <p className="text-[13px] text-[#050013]">New safety guidelines for night rides.</p>
                          <button className="mt-2 px-4 py-1 rounded-full bg-cstm-blue-700 text-white text-[12px]">View</button>
                        </div>
                        <span className="ml-auto text-xs text-[#76787A]">2 hr ago</span>
                      </div>
                    </div>
                    <div className="p-4">
                      <div className="flex items-start gap-3">
                        <div className="p-2 rounded-[20px] bg-yellow-100 flex items-center justify-center">
                          <span className="text-yellow-600 text-lg"><LuCalendarCheck2 /></span>
                        </div>
                        <div className="text-sm">
                          <p className="font-semibold text-[#050013]">Weekly Summary</p>
                          <p className="text-[13px] text-[#050013]">You completed 42 trips this week.</p>
                        </div>
                        <span className="ml-auto text-xs text-[#76787A]">2 hr ago</span>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          {/* end  */}
            <UserDropdown />
          </div>
        </div>
      </div>
      

{/* mobile - hearder - start  */}
{showMobileHeader && (
  <div className="lg:hidden block rounded-b-2xl bg-gradient-to-r from-blue-500 via-indigo-500 to-teal-400">
    <div className="flex items-center px-4 py-5 justify-between">
      {/* Left: Hamburger + Logo */}
      <div className="flex items-center gap-3">
        <button onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)} className="text-white text-2xl focus:outline-none">
        <TbMenuDeep className="scale-x-[-1]"/>
        </button>
        <div className="text-white text-2xl">
        <Image width={154} height={32} className="dark:hidden" src="/images/sidebar/logo.png" alt="Logo" />
        <Image width={154} height={32} className="hidden dark:block" src="./images/logo/logo-dark.svg" alt="Logo" />
        </div>
      </div>

      {/* Right: Icons */}
      <div className="flex items-center space-x-4">
      <button className="bg-white/30 p-[12px] rounded-full" onClick={() => setIsMobileSearchOpen(!isMobileSearchOpen)}>
  <IoIosSearch className="text-white text-xl" />
</button>
        <div>
        <div className="relative" ref={dropdownRef}>
        <button
    onClick={() => setShowNewNotifications(!showNewNotifications)}
    className="relative dropdown-toggle flex items-center justify-center text-white transition-colors bg-white/20 rounded-full h-11 w-11 hover:bg-white/30"
  >
    <HiOutlineBell className="text-white text-xl" />
    
    {/* Red dot indicator */}
    <span className="absolute top-[12px] left-[22px] w-[9px] h-[9px] bg-red-500 rounded-full"></span>
  </button>
              {showNewNotifications && (
                <div className="absolute right-0 mt-2 w-[380px] bg-white rounded-lg shadow-xl z-50">
                  <div className="absolute -top-2 right-6 w-4 h-4 bg-tables transform rotate-45"></div>
                  <div className="px-4 py-3 flex justify-between items-center bg-tables">
                    <h4 className="text-[16px] font-normal text-[#050013]">Notifications</h4>
                    <span className="text-[13px] text-btn cursor-pointer flex items-center"><IoCheckmarkDoneOutline className="pr-2 text-[24px] text-[#76787A]" /> Mark as all read</span>
                  </div>
                  <div className="max-h-[400px] overflow-y-auto custom-scrollbar">
                    <div className="p-4 border-b">
                      <p className="text-[12px] text-[#76787A] mb-2">Today</p>
                      <div className="flex items-start gap-3">
                        <div className="p-2 rounded-[20px] bg-purple-100 flex items-center justify-center">
                          <span className="text-purple-600 text-lg"><LiaCarSideSolid />
                          </span>
                        </div>
                        <div className="text-sm">
                          <p className="font-semibold text-[#050013]">New Ride Request</p>
                          <p className="text-[13px] text-[#050013]">From: Spuistraat City to Schipol Airport</p>
                          <button className="mt-2 px-4 py-1 rounded-full bg-cstm-blue-700 text-white text-[12px]">View</button>
                        </div>
                        <span className="ml-auto text-xs text-[#76787A]">15 mins ago</span>
                      </div>
                    </div>
                    <div className="p-4 border-b">
                      <div className="flex items-start gap-3">
                        <div className="p-2 rounded-[20px] bg-green-100 flex items-center justify-center">
                          <span className="text-green-600 text-lg"><LuWallet /></span>
                        </div>
                        <div className="text-sm">
                          <p className="font-semibold text-[#050013]">Payment Received</p>
                          <span className="flex">
                            <p className="text-btn text-[13px] pr-1">$24.50</p>
                            <p className="text-[13px] text-[#050013]">received for trip #2847</p>
                          </span>
                        </div>
                        <span className="ml-auto text-xs text-[#76787A]">1 hr ago</span>
                      </div>
                    </div>
                    <div className="p-4 border-b">
                      <p className="text-[12px] text-[#76787A] mb-2">Earlier</p>
                      <div className="flex items-start gap-3">
                        <div className="p-2 rounded-[20px] bg-pink-100 flex items-center justify-center">
                          <span className="text-pink-600 text-lg"><HiOutlineBell />
                          </span>
                        </div>
                        <div className="text-sm">
                          <p className="font-semibold text-[#050013]">Policy Update</p>
                          <p className="text-[13px] text-[#050013]">New safety guidelines for night rides.</p>
                          <button className="mt-2 px-4 py-1 rounded-full bg-cstm-blue-700 text-white text-[12px]">View</button>
                        </div>
                        <span className="ml-auto text-xs text-[#76787A]">2 hr ago</span>
                      </div>
                    </div>
                    <div className="p-4">
                      <div className="flex items-start gap-3">
                        <div className="p-2 rounded-[20px] bg-yellow-100 flex items-center justify-center">
                          <span className="text-yellow-600 text-lg"><LuCalendarCheck2 /></span>
                        </div>
                        <div className="text-sm">
                          <p className="font-semibold text-[#050013]">Weekly Summary</p>
                          <p className="text-[13px] text-[#050013]">You completed 42 trips this week.</p>
                        </div>
                        <span className="ml-auto text-xs text-[#76787A]">2 hr ago</span>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
        </div>
        <img
          src="https://via.placeholder.com/30"
          alt="Profile"
          className="w-8 h-8 rounded-full object-cover"
        />
      </div>
    </div>

    {/* Search Dropdown Bar */}
{isMobileSearchOpen && (
  <div className="mb-3 px-1">
    <div className="bg-white rounded-lg shadow-md p-2">
      <input
        type="text"
        placeholder="Search here..."
        className="w-full border border-gray-300 rounded-md px-4 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-400"
      />
    </div>
  </div>
)} 
    {/* Tabs */}
    <div className="bg-tables dark:bg-gray-800 py-6 px-4 rounded-t-[15px]">
    <div className="flex items-center justify-center rounded-full bg-white ">
              {["available", "break", "unavailable"].map((opt) => {
                const labels = {
                  available: "Available",
                  break: "Break",
                  unavailable: "Unavailable",
                };
                const active = opt === availability;
                return (
                  <button
                    key={opt}
                    onClick={() => setAvailability(opt)}
                    className={`px-6 py-1.5 text-[16px] w-full font-normal rounded-full transition-colors duration-200  ${active
                      ? opt === "available"
                        ? "bg-[linear-gradient(183deg,_#079F61_7.15%,_#18EC95_96.74%)] text-white"
                        : opt === "break"
                          ? "bg-[linear-gradient(284.14deg,_#C78C35_3.57%,_#F79E18_106.42%)] text-white"
                          : "bg-[linear-gradient(284.14deg,_#C83434_3.57%,_#FB6565_106.42%)] text-white"
                      : "text-[#76787A] dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700"
                      }`}
                  >
                    {labels[opt]}
                  </button>
                );
              })}
            </div>
            </div>
 {/* Slide-down Menu */}
    {isMobileMenuOpen && (
   <div
   className={`w-[90vw] max-w-xs h-full bg-white shadow-xl rounded-r-2xl flex flex-col justify-between fixed top-0 
    left-0 h-full w-3/4 z-50 transform transition-transform duration-300 
    ease-in-out ${
     isMobileMenuOpen ? 'translate-x-0' : '-translate-x-full'
   }`}
 >
   {/* Close Button */}
   {/* <div className="flex justify-end p-4">
     <button onClick={() => setIsMobileMenuOpen(false)} className="text-gray-600 text-2xl">×</button>
   </div> */}
 {/* TOP SECTION */}
<div>
  {/* Profile */}
  <div className="flex items-center gap-3 p-6 border-b">
    <img src="/images/avatar.jpg" alt="Profile" className="w-12 h-12 rounded-full object-cover" />
    <div>
      <p className="text-[16px] font-medium text-[#050013] mb-1">Rivka Frank</p>
      <p className="text-sm text-[#76787A]">ID: <span className="text-purple">DRV-2025-001</span></p>
    </div>
  </div>

  {/* Menu */}
  <div className="space-y-5 text-base text-gray-800">
    <Link href="/" className="flex items-center gap-3 px-6 pt-6 text-[16px] font-medium text-[#050013]"><TbDashboard size={25} className="text-[#76787A]" />Dashboard</Link>
    <Link href="/schedule" className="flex items-center gap-3 px-6 pt-3 text-[16px] font-medium text-[#050013]"><TbCalendarDue size={25} className="text-[#76787A]" />Schedule</Link>
    <Link href="/chat" className="flex items-center gap-3 relative px-6 pt-3 text-[16px] font-medium text-[#050013]">
      <BsChatLeftDots size={20} className="text-[#76787A]" /> Chat
      <span className="bg-indigo-600 text-white text-xs px-2 py-1 rounded-full">3</span>
    </Link>
    <Link href="/report" className="flex items-center gap-3 px-6 pt-3 text-[16px] font-medium text-[#050013]"><FiInfo size={25} className="text-[#76787A]" />Report</Link>
    <Link href="/earnings" className="flex items-center gap-3 px-6 pt-3 text-[16px] font-medium text-[#050013]"><LuWallet size={25} className="text-[#76787A]" />My Earnings</Link>
    <Link href="/vehicle" className="flex items-center gap-3 px-6 pt-3 text-[16px] font-medium text-[#050013]"><LuWallet size={25} className="text-[#76787A]" />Vehicle Info</Link>
  </div>
  <div className="p-6">
  <button className="w-full flex items-center justify-center gap-3 bg-[#FF5F5F] text-[14px] text-white font-normal py-2 rounded-full shadow">
    <span className="bg-white rounded-full p-[8px] text-[#050013]"><FiPhoneCall size={20}/></span>
    Emergency support <span className="font-normal">24/7</span>
  </button>
  </div>
</div>

{/* BOTTOM SECTION */}
<div className="p-6">
  <button className="w-full border border-indigo-600 text-indigo-600 font-medium py-2 rounded-full">
    Sign out
  </button>
</div>


 </div>
    )}
  </div>
)}





{/* mobile - header - end  */}


    </header>

  );
};

export default WebDriverHeader;