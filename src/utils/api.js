const API_BASE_URL = "http://localhost:3300";

const getHeaders = () => {
    const headers = {
        "Content-Type": "application/json",
        Accept: "application/json",
    };
    const token = localStorage.getItem("authToken");
    if (token) {
        headers["Authorization"] = `Bearer ${token}`;
    }
    return headers;
};

const request = async (endpoint, method = "GET", body = null, options = {}) => {
    const config = {
        method,
        headers: { ...getHeaders(), ...options.headers },
        ...options,
    };

    if (body) {
        config.body = JSON.stringify(body);
    }

    try {
        const response = await fetch(`${API_BASE_URL}${endpoint}`, config);
        const data = await response.json();

        if (!response.ok) {
            throw new Error(data.message || "Something went wrong!");
        }

        return data;
    } catch (error) {
        console.error("API Error:", error);
        throw error;
    }
};

export const apiService = {
    get: (endpoint, params = {}) => {
        const url = new URL(`${API_BASE_URL}${endpoint}`);
        Object.keys(params).forEach(key => url.searchParams.append(key, params[key]));
        return request(url, "GET");
    },
    post: (endpoint, body = {}, options = {}) => request(endpoint, "POST", body, options),
    put: (endpoint, body = {}, options = {}) => request(endpoint, "PUT", body, options),
    patch: (endpoint, body = {}, options = {}) => request(endpoint, "PATCH", body, options),
    delete: (endpoint, options = {}) => request(endpoint, "DELETE", null, options),
    postFileUpload: (endpoint, formData) => {
        return fetch(`${API_BASE_URL}${endpoint}`, {
            method: "POST",
            headers: {
                Authorization: `Bearer ${localStorage.getItem("authToken")}`,
            },
            body: formData,
        })
            .then(response => response.json())
            .catch(error => {
                console.error("File Upload Error:", error);
                throw error;
            });
    },
    havePermissions: (permission) => {
        const userPermissions = JSON.parse(localStorage.getItem("permissions") || "[]");
        return userPermissions.includes(permission);
    },
};
