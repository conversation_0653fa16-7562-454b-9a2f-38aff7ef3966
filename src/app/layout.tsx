"use client";

import { Outfit } from "next/font/google";
import "./globals.css";
import { SidebarProvider } from "@/context/SidebarContext";
import { ThemeProvider } from "@/context/ThemeContext";
import Image from "next/image";
import React, { useEffect, useState } from "react";
import SignIn from "@/app/(full-width-pages)/(auth)/signin/page";
import { GlobalContextProvider } from "@/hooks/useGlobalContext";
import { ToastContainer } from "react-toastify";
import { SideMenuBottom } from "@/icons";
import 'react-toastify/dist/ReactToastify.css';
const outfit = Outfit({
    variable: "--font-outfit-sans",
    subsets: ["latin"],
});

export default function RootLayout({ children }: Readonly<{ children: React.ReactNode }>) {
    const [isLoggedIn, setIsLoggedIn] = useState<boolean | null>(false);
    useEffect(() => {
        const loggedIn = localStorage.getItem("isLoggedIn");
        if (loggedIn) {
            setIsLoggedIn(true)
        }
    }, []);
    return (
        <html lang="en">
            <body className={`${outfit.variable} dark:bg-gray-900`}>
                <GlobalContextProvider>
                    <ToastContainer
                        position="top-right"
                        autoClose={5000}
                        hideProgressBar={false}
                        newestOnTop={false}
                        closeOnClick={false}
                        rtl={false}
                        pauseOnFocusLoss
                        draggable
                        pauseOnHover
                        theme="light"
                    />
                    {isLoggedIn ? (
                        <ThemeProvider>
                            <SidebarProvider>{children}</SidebarProvider>
                        </ThemeProvider>
                    ) : (
                        <div className="relative p-6 bg-white z-1 sm:p-0">
                            <div className="relative flex lg:flex-row w-full h-screen justify-center flex-col sm:p-0">
                                <div
                                    className="lg:w-1/2 w-full h-full dark:bg-white/5 lg:grid items-center hidden relative"
                                >
                                    <Image className="w-full h-screen"
                                        width={100}
                                        height={100}
                                        src="/images/bg-image/login_bg.png"
                                        alt="background-image"
                                    />
                                    <Image
                                        className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2"
                                        width={219}
                                        height={48}
                                        src="/images/logo/auth-logo.png"
                                        alt="logo-image"
                                    />
                                </div>
                                <SignIn />
                            </div>
                        </div>
                    )}
                </GlobalContextProvider>

            </body>
        </html>
    );
}
