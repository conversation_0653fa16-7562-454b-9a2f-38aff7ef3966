import Image from "next/image";
import React from "react";

export default function AuthLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <div className="relative p-6 bg-white z-1  sm:p-0">
        <div className="relative flex lg:flex-row w-full h-screen justify-center flex-col sm:p-0">
          <div className="lg:w-1/2 w-full h-full bg-brand-950 dark:bg-white/5 lg:grid items-center hidden"
            style={{
              background:
                "linear-gradient(rgb(24, 236, 149), rgb(37, 138, 187), rgb(55, 7, 239), rgb(112, 24, 235))",
            }}
          >
            <div className="relative items-center justify-center flex z-1">
              <div className="flex flex-col items-center max-w-xs">
                <Image
                  width={231}
                  height={48}
                  src="/images/logo/auth-logo.png"
                  alt="Logo"
                />
              </div>
            </div>
          </div>
          {children}
        </div>
    </div>
  );
}
