'use client'
import {
    Table,
    TableBody,
    Table<PERSON>ell,
    TableHeader,
    TableRow,
} from '@/components/ui/table';
import Badge from '../../../../../components/ui/badge/Badge';
import Tippy from '@tippyjs/react';
import 'tippy.js/dist/tippy.css';
import { PiClockCounterClockwiseLight, PiDotsThreeVerticalBold } from 'react-icons/pi';
import { StarIcon } from '@/icons';
import { FaStar, FaFilter, FaDownload } from 'react-icons/fa';
import { fetchDriverActivityLogs } from '../state/queries';
import { useEffect, useMemo, useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { FiChevronDown, FiChevronUp } from 'react-icons/fi';

interface Trip {
    tripId: number;
    dateTime: string;
    startLocation: string;
    endLocation: string;
    status: string;
    distance: number;
    duration: number;
    earnings: number;
    rating: number;
    feedback: string;
    driver: {
        id: number;
        name: string;
        email: string;
    };
}

export default function DriverActivityTable({ driverDetails }: { driverDetails: any }) {
    const [selectedTrips, setSelectedTrips] = useState<number[]>([]);
    const driverId = driverDetails?.id?.toString();
    const [sortConfig, setSortConfig] = useState<{ key: string; direction: 'asc' | 'desc' } | null>(null);

    const {
        data: trips = [],
        isLoading,
        isError,
        error,
        refetch,
    } = useQuery({
        queryKey: ['driverTrips', driverId],
        queryFn: () => {
            if (!driverId) throw new Error('Driver ID is missing');
            return fetchDriverActivityLogs(driverId);
        },
        enabled: !!driverId, // only runs when driverId is present
    });

    const formatDateTime = (dateString: string) => {
        const date = new Date(dateString);
        return date.toLocaleString('en-US', {
            month: 'short',
            day: 'numeric',
            year: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
        });
    };

    const formatDuration = (minutes: number) => {
        const hours = Math.floor(minutes / 60);
        const mins = Math.floor(minutes % 60);
        return `${hours}h ${mins}m`;
    };

    const SortIcon = ({ direction, active }: { direction: 'asc' | 'desc'; active: boolean }) => {
        let icon: string;
        if (!active) {
            icon = '⇅';
        } else {
            icon = direction === 'asc' ? '↑' : '↓';
        }

        return (
            <span
                className={`inline ml-1 text-[13px] ${active ? 'text-gray-700' : 'text-gray-400'} cursor-pointer hover:text-gray-600 transition-colors`}
            >
                {icon}
            </span>
        );
    };


    const handleSort = (key: keyof Trip) => {
        setSortConfig(prev => {
            if (prev?.key === key) {
                return {
                    key,
                    direction: prev.direction === 'asc' ? 'desc' : 'asc',
                };
            }
            return { key, direction: 'asc' };
        });
    };

    const sortedTrips = useMemo(() => {
        if (!sortConfig) return trips;

        const sorted = [...trips].sort((a, b) => {
            const valA = a[sortConfig.key];
            const valB = b[sortConfig.key];

            if (valA == null) return 1;
            if (valB == null) return -1;

            if (typeof valA === 'string' && typeof valB === 'string') {
                return sortConfig.direction === 'asc'
                    ? valA.localeCompare(valB)
                    : valB.localeCompare(valA);
            }

            if (typeof valA === 'number' && typeof valB === 'number') {
                return sortConfig.direction === 'asc' ? valA - valB : valB - valA;
            }

            if (valA instanceof Date && valB instanceof Date) {
                return sortConfig.direction === 'asc'
                    ? valA.getTime() - valB.getTime()
                    : valB.getTime() - valA.getTime();
            }

            return 0;
        });

        return sorted;
    }, [trips, sortConfig]);



    const getStatusColor = (status: string) => {
        switch (status.toLowerCase()) {
            case 'completed':
                return 'success';
            case 'cancelled':
                return 'error';
            case 'in progress':
                return 'warning';
            default:
                return 'primary';
        }
    };

    const handleSelectTrip = (tripId: number) => {
        setSelectedTrips(prev =>
            prev.includes(tripId)
                ? prev.filter(id => id !== tripId)
                : [...prev, tripId]
        );
    };

    const handleSelectAll = () => {
        if (selectedTrips.length === trips.length) {
            setSelectedTrips([]);
        } else {
            setSelectedTrips(trips.map(trip => trip.tripId));
        }
    };

    if (isLoading) {
        return <div className="flex justify-center items-center h-64">Loading trips...</div>;
    }

    if (isError) {
        return <div className="text-red-500 p-4">Error: {error.message}</div>;
    }

    if (trips.length === 0) {
        return <div className="text-blue-500 p-4">No trips found for this driver.</div>;
    }

    return (
        <div className="tabled">
            <div className="overflow-hidden bg-white dark:border-white/[0.05] dark:bg-white/[0.03] rounded-b-[12px]">
                <div className="header-bar flex items-center justify-between p-3 bg-tables dark:bg-gray-800">
                    <div className="flex items-center space-x-4">

                    </div>
                    <div className="flex items-center gap-2">
                        <button
                            type="button"
                            className="flex items-center gap-2 py-2 px-3 text-xs font-medium text-[#76787A] focus:outline-none bg-white rounded-full border border-gray-200 hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-4 focus:ring-gray-100 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700"
                        >
                            <svg
                                xmlns="http://www.w3.org/2000/svg"
                                fill="none"
                                viewBox="0 0 24 24"
                                strokeWidth="1.5"
                                stroke="currentColor"
                                className="w-4 h-4"
                            >
                                <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    d="M12 3c2.755 0 5.455.232 8.083.678.533.09.917.556.917 1.096v1.044a2.25 2.25 0 0 1-.659 1.591l-5.432 5.432a2.25 2.25 0 0 0-.659 1.591v2.927a2.25 2.25 0 0 1-1.244 2.013L9.75 21v-6.568a2.25 2.25 0 0 0-.659-1.591L3.659 7.409A2.25 2.25 0 0 1 3 5.818V4.774c0-.54.384-1.006.917-1.096A48.32 48.32 0 0 1 12 3Z"
                                />
                            </svg>
                            Filters
                        </button>
                        <button
                            type="button"
                            aria-label="refresh"
                            className="flex items-center justify-center p-1 text-[#76787A] focus:outline-none bg-white rounded-full border border-gray-200 hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-4 focus:ring-gray-100 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700"
                        >
                            <PiClockCounterClockwiseLight size={22} />
                        </button>
                        <button
                            type="button"
                            aria-label="download"
                            className="flex items-center justify-center p-2 text-[#76787A] focus:outline-none bg-white rounded-full border border-gray-200 hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-4 focus:ring-gray-100 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700"
                        >
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"
                                className="size-4">
                                <path fillRule="evenodd"
                                    d="M12 2.25a.75.75 0 0 1 .75.75v11.69l3.22-3.22a.75.75 0 1 1 1.06 1.06l-4.5 4.5a.75.75 0 0 1-1.06 0l-4.5-4.5a.75.75 0 1 1 1.06-1.06l3.22 3.22V3a.75.75 0 0 1 .75-.75Zm-9 13.5a.75.75 0 0 1 .75.75v2.25a1.5 1.5 0 0 0 1.5 1.5h13.5a1.5 1.5 0 0 0 1.5-1.5V16.5a.75.75 0 0 1 1.5 0v2.25a3 3 0 0 1-3 3H5.25a3 3 0 0 1-3-3V16.5a.75.75 0 0 1 .75-.75Z"
                                    clip-rule="evenodd" />
                            </svg>
                            {/* <FaDownload size={20} /> */}
                        </button>
                    </div>
                </div>
                <div className="max-w-full overflow-x-auto custom-scrollbar">
                    <div className="max-w-[900px] min-w-[-webkit-fill-available]">
                        <Table>
                            <TableHeader className="border-b bg-tables dark:border-white/[0.05]">
                                <TableRow className='border-t'>
                                    <TableCell isHeader className="px-4 py-3 font-medium text-gray-500 text-start text-xs dark:text-gray-400 w-10">
                                        <input
                                            type="checkbox"
                                            checked={selectedTrips.length === trips.length && trips.length > 0}
                                            onChange={handleSelectAll}
                                            className="form-checkbox h-4 w-4 text-blue-600 rounded"
                                        />
                                    </TableCell>


                                    <TableCell
                                        isHeader
                                        className="px-4 py-3 font-medium text-gray-500 text-start text-[12px] whitespace-nowrap select-none"
                                    >
                                        <div
                                            onClick={() => handleSort('tripId')}
                                            className="cursor-pointer flex items-center"
                                            role="button"
                                            tabIndex={0}
                                        >
                                            Trip ID
                                            <SortIcon
                                                direction={sortConfig?.key === 'tripId' ? sortConfig.direction : 'asc'}
                                                active={sortConfig?.key === 'tripId'}
                                            />
                                        </div>
                                    </TableCell>

                                    <TableCell isHeader className="px-4 py-3 font-medium text-gray-500 text-start text-xs dark:text-gray-400 whitespace-nowrap">
                                        <div
                                            onClick={() => handleSort('dateTime')}
                                            className="cursor-pointer flex items-center"
                                            role="button"
                                            tabIndex={0}
                                        >
                                            Date/Time
                                            <SortIcon
                                                direction={sortConfig?.key === 'dateTime' ? sortConfig.direction : 'asc'}
                                                active={sortConfig?.key === 'dateTime'}
                                            />
                                        </div>
                                    </TableCell>
                                    <TableCell isHeader className="px-4 py-3 font-medium text-gray-500 text-start text-xs dark:text-gray-400 whitespace-nowrap">
                                        <div
                                            onClick={() => handleSort('startLocation')}
                                            className="cursor-pointer flex items-center"
                                            role="button"
                                            tabIndex={0}
                                        >
                                            Start Location
                                            <SortIcon
                                                direction={sortConfig?.key === 'startLocation' ? sortConfig.direction : 'asc'}
                                                active={sortConfig?.key === 'startLocation'}
                                            />
                                        </div>
                                    </TableCell>
                                    <TableCell isHeader className="px-4 py-3 font-medium text-gray-500 text-start text-xs dark:text-gray-400 whitespace-nowrap">
                                        <div
                                            onClick={() => handleSort('endLocation')}
                                            className="cursor-pointer flex items-center"
                                            role="button"
                                            tabIndex={0}
                                        >
                                            End Location
                                            <SortIcon
                                                direction={sortConfig?.key === 'endLocation' ? sortConfig.direction : 'asc'}
                                                active={sortConfig?.key === 'endLocation'}
                                            />
                                        </div>
                                    </TableCell>
                                    <TableCell isHeader className="px-4 py-3 font-medium text-gray-500 text-start text-xs dark:text-gray-400 whitespace-nowrap">
                                        <div
                                            onClick={() => handleSort('status')}
                                            className="cursor-pointer flex items-center"
                                            role="button"
                                            tabIndex={0}
                                        >
                                            Status
                                            <SortIcon
                                                direction={sortConfig?.key === 'status' ? sortConfig.direction : 'asc'}
                                                active={sortConfig?.key === 'status'}
                                            />
                                        </div>
                                    </TableCell>
                                    <TableCell isHeader className="px-4 py-3 font-medium text-gray-500 text-start text-xs dark:text-gray-400 whitespace-nowrap">
                                        <div
                                            onClick={() => handleSort('distance')}
                                            className="cursor-pointer flex items-center"
                                            role="button"
                                            tabIndex={0}
                                        >
                                            Distance
                                            <SortIcon
                                                direction={sortConfig?.key === 'distance' ? sortConfig.direction : 'asc'}
                                                active={sortConfig?.key === 'distance'}
                                            />
                                        </div>
                                    </TableCell>
                                    <TableCell isHeader className="px-4 py-3 font-medium text-gray-500 text-start text-xs dark:text-gray-400 whitespace-nowrap">

                                        <div
                                            onClick={() => handleSort('duration')}
                                            className="cursor-pointer flex items-center"
                                            role="button"
                                            tabIndex={0}
                                        >
                                            Duration
                                            <SortIcon
                                                direction={sortConfig?.key === 'duration' ? sortConfig.direction : 'asc'}
                                                active={sortConfig?.key === 'duration'}
                                            />
                                        </div>
                                    </TableCell>
                                    <TableCell isHeader className="px-4 py-3 font-medium text-gray-500 text-start text-xs dark:text-gray-400 whitespace-nowrap">

                                        <div
                                            onClick={() => handleSort('earnings')}
                                            className="cursor-pointer flex items-center"
                                            role="button"
                                            tabIndex={0}
                                        >
                                            Earnings
                                            <SortIcon
                                                direction={sortConfig?.key === 'earnings' ? sortConfig.direction : 'asc'}
                                                active={sortConfig?.key === 'earnings'}
                                            />
                                        </div>
                                    </TableCell>
                                    <TableCell isHeader className="px-4 py-3 font-medium text-gray-500 text-start text-xs dark:text-gray-400 whitespace-nowrap">

                                        <div
                                            onClick={() => handleSort('rating')}
                                            className="cursor-pointer flex items-center"
                                            role="button"
                                            tabIndex={0}
                                        >
                                            Ratings
                                            <SortIcon
                                                direction={sortConfig?.key === 'rating' ? sortConfig.direction : 'asc'}
                                                active={sortConfig?.key === 'rating'}
                                            />
                                        </div>
                                    </TableCell>
                                    <TableCell isHeader className="px-4 py-3 font-medium text-gray-500 text-start text-xs dark:text-gray-400 whitespace-nowrap">
                                        Feedback
                                    </TableCell>
                                    <TableCell isHeader className="px-4 py-3 font-medium text-gray-500 text-start text-xs dark:text-gray-400 whitespace-nowrap" children={''}>

                                    </TableCell>
                                </TableRow>
                            </TableHeader>
                            <TableBody className="divide-y bg-tables dark:divide-white/[0.05]">
                                {sortedTrips.map((trip) => (
                                    <TableRow key={trip.tripId} className="dark:hover:bg-gray-800">
                                        <TableCell className="px-4 py-3 w-10">
                                            <input
                                                type="checkbox"
                                                checked={selectedTrips.includes(trip.tripId)}
                                                onChange={() => handleSelectTrip(trip.tripId)}
                                                className="form-checkbox h-4 w-4 text-blue-600 rounded"
                                            />
                                        </TableCell>
                                        <TableCell className="px-4 py-3">
                                            <p className="text-[#050013] text-xs font-medium whitespace-nowrap">
                                                #{String(trip.tripId).padStart(5, '0')}
                                            </p>
                                        </TableCell>
                                        <TableCell className="px-4 py-3">
                                            <p className="text-[#050013] text-xs whitespace-nowrap">
                                                {formatDateTime(trip.dateTime)}
                                            </p>
                                        </TableCell>
                                        <TableCell className="px-4 py-3">
                                            <p className="text-[#050013] text-xs whitespace-nowrap">{trip.startLocation}</p>
                                        </TableCell>
                                        <TableCell className="px-4 py-3">
                                            <p className="text-[#050013] text-xs whitespace-nowrap">{trip.endLocation}</p>
                                        </TableCell>
                                        <TableCell className="px-4 py-3">
                                            <Badge size="sm" color={getStatusColor(trip.status)}>
                                                <span className='capitalize'>{trip.status}</span>
                                            </Badge>
                                        </TableCell>
                                        <TableCell className="px-4 py-3">
                                            <p className="text-[#050013] text-xs whitespace-nowrap">{trip.distance.toFixed(2)}</p>
                                        </TableCell>
                                        <TableCell className="px-4 py-3">
                                            <p className="text-[#050013] text-xs whitespace-nowrap">{formatDuration(trip.duration)}</p>
                                        </TableCell>
                                        <TableCell className="px-4 py-3">
                                            <p className="text-[#050013] text-xs whitespace-nowrap">${trip.earnings.toFixed(2)}</p>
                                        </TableCell>

                                        <TableCell
                                            className="px-4 py-3 ">
                                            <p className="text-[#050013] text-[12px] flex items-center gap-1">{trip?.rating} <StarIcon /></p>
                                        </TableCell>
                                        <TableCell className="px-4 py-3 max-w-[200px]">
                                            <p className="text-[#050013] text-xs line-clamp-2">{trip.feedback}</p>
                                        </TableCell>
                                        <TableCell className="px-4 py-3">
                                            <Tippy
                                                content={
                                                    <div className="bg-white text-gray-900">
                                                        <div className="flex flex-col space-y-1 p-1">
                                                            <button className="text-[#76787A] hover:text-[#050013] px-2 py-1 rounded w-full text-start">
                                                                View Trip Details
                                                            </button>
                                                            <button className="text-[#76787A] hover:text-[#050013] px-2 py-1 rounded w-full text-start">
                                                                Show Feedback
                                                            </button>
                                                            <button className="text-[#76787A] hover:text-[#050013] px-2 py-1 rounded w-full text-start">
                                                                Report an Issue
                                                            </button>
                                                            <button className="text-[#76787A] hover:text-[#050013] px-2 py-1 rounded w-full text-start">
                                                                Resolve Payment
                                                            </button>
                                                            <button className="text-[#76787A] hover:text-[#050013] px-2 py-1 rounded w-full text-start">
                                                                Mark as Reviewed
                                                            </button>
                                                            <button className="text-[#76787A] hover:text-[#050013] px-2 py-1 rounded w-full text-start">
                                                                Export Trip Report
                                                            </button>
                                                            <button className="text-[#76787A] hover:text-[#050013] px-2 py-1 rounded w-full text-start">
                                                                Send Reminder To Driver
                                                            </button>
                                                            <button className="text-[#76787A] hover:text-[#050013] px-2 py-1 rounded w-full text-start">
                                                                Add Tags to Trip
                                                            </button>
                                                            <button className="text-[#76787A] hover:text-[#050013] px-2 py-1 rounded w-full text-start">
                                                                Download Trip Receipt
                                                            </button>
                                                            <button className="text-[#76787A] hover:text-[#050013] px-2 py-1 rounded w-full text-start">
                                                                Reassign Trip
                                                            </button>
                                                            <button className="text-[#76787A] hover:text-[#050013] px-2 py-1 rounded w-full text-start">
                                                                Delete Trip
                                                            </button>
                                                        </div>
                                                    </div>
                                                }
                                                interactive={true}
                                                placement="left"
                                                theme="light"
                                                arrow={false}
                                                duration={0}
                                                className="!bg-white !text-gray-900 border border-gray-200 rounded-lg shadow-sm"
                                            >
                                                <button type="button" className="focus:outline-none" aria-label="actions">
                                                    <PiDotsThreeVerticalBold size={20} />
                                                </button>
                                            </Tippy>
                                        </TableCell>
                                    </TableRow>
                                ))}
                            </TableBody>
                        </Table>
                    </div>
                </div>
            </div>
        </div>
    );
}