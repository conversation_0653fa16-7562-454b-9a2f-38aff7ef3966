'use client'
import {
    Table,
    TableBody,
    Table<PERSON>ell,
    TableHeader,
    TableRow,
} from '@/components/ui/table';
import Badge from '../../../../../components/ui/badge/Badge';
import Tippy from '@tippyjs/react';
import 'tippy.js/dist/tippy.css';
import { useState, useEffect, useMemo, JSXElementConstructor, ReactElement, ReactNode, ReactPortal } from 'react';
import { PiClockCounterClockwiseLight } from 'react-icons/pi';
import { fetchDriverSupportLogs } from '../state/queries';
import { BsThreeDotsVertical } from 'react-icons/bs';
import { FiChevronUp, FiChevronDown } from 'react-icons/fi';
import { useQuery } from '@tanstack/react-query';

interface SupportLog {
    id: number;
    issue_id: string;
    issue_type: string;
    issue_description: string;
    priority: string;
    status: string;
    resolution_date: string | null;
    assigned_admin: string;
    created_at: string;
    driverName: string;
    driverEmail: string;
    driverPhone: string;
    driverImage: string;
    driverStatus: string;
    driverRegion: string;
}

interface DocumentTabProps {
    driverDetails: any;
}

const formatDateTime = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
    });
};

const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
        case 'resolved':
            return 'success';
        case 'pending':
            return 'warning';
        case 'cancelled':
            return 'error';
        default:
            return 'primary';
    }
};

const getPriorityColor = (priority: string) => {
    switch (priority.toLowerCase()) {
        case 'high':
            return 'error';
        case 'medium':
            return 'warning';
        case 'low':
            return 'success';
        default:
            return 'primary';
    }
};


const SortIcon = ({ direction, active }: { direction: 'asc' | 'desc'; active: boolean }) => {
    let icon: string | number | bigint | boolean | ReactElement<unknown, string | JSXElementConstructor<any>> | Iterable<ReactNode> | Promise<string | number | bigint | boolean | ReactPortal | ReactElement<unknown, string | JSXElementConstructor<any>> | Iterable<ReactNode>>;

    if (!active) {
        icon = '⇅';
    } else {
        icon = direction === 'asc' ? '↑' : '↓';
    }

    return (
        <span
            className={`inline ml-1 text-[13px] ${active ? 'text-gray-700' : 'text-gray-400'} cursor-pointer hover:text-gray-600 transition-colors`}
        >
            {icon}
        </span>
    );
};



export default function Supportlog({ driverDetails }: DocumentTabProps) {
    const driverId = driverDetails?.id;
    const [sortConfig, setSortConfig] = useState<{ key: keyof SupportLog; direction: 'asc' | 'desc' } | null>(null);

    const {
        data: supportLogs = [],
        isLoading,
        isError,
        error,
        refetch,
    } = useQuery<SupportLog[], Error>({
        queryKey: ['driverSupportLogs', driverId],
        queryFn: () => fetchDriverSupportLogs(driverId.toString()),
        enabled: !!driverId, // only fetch if driverId exists
    });

    const handleSort = (key: keyof SupportLog) => {
        setSortConfig((prevConfig) =>
            prevConfig?.key === key
                ? { key, direction: prevConfig.direction === 'asc' ? 'desc' : 'asc' }
                : { key, direction: 'asc' }
        );
    };

    const sortedLogs = useMemo(() => {
        if (!sortConfig) return supportLogs;

        const sorted = [...supportLogs].sort((a, b) => {
            const valA = a[sortConfig.key];
            const valB = b[sortConfig.key];

            if (valA == null) return 1;
            if (valB == null) return -1;

            if (['created_at', 'resolution_date'].includes(sortConfig.key)) {
                return sortConfig.direction === 'asc'
                    ? new Date(valA).getTime() - new Date(valB).getTime()
                    : new Date(valB).getTime() - new Date(valA).getTime();
            }

            // If sorting by string
            if (typeof valA === 'string' && typeof valB === 'string') {
                return sortConfig.direction === 'asc'
                    ? valA.localeCompare(valB)
                    : valB.localeCompare(valA);
            }

            // Fallback
            return sortConfig.direction === 'asc'
                ? (valA as any) > (valB as any)
                    ? 1
                    : -1
                : (valA as any) < (valB as any)
                    ? 1
                    : -1;
        });

        return sorted;
    }, [supportLogs, sortConfig]);


    if (isLoading) {
        return (
            <div className="flex justify-center items-center h-64">
                <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
            </div>
        );
    }

    if (isError) {
        return (
            <div className="bg-red-50 border-l-4 border-red-500 p-4">
                <div className="flex">
                    <div className="flex-shrink-0">
                        <svg className="h-5 w-5 text-red-500" viewBox="0 0 20 20" fill="currentColor">
                            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                        </svg>
                    </div>
                    <div className="ml-3">
                        <p className="text-sm text-red-700">{error.message}</p>
                    </div>
                </div>
            </div>
        );
    }

    if (supportLogs.length === 0) {
        return (
            <div className="bg-blue-50 border-l-4 border-blue-500 p-4">
                <div className="flex">
                    <div className="flex-shrink-0">
                        <svg className="h-5 w-5 text-blue-500" viewBox="0 0 20 20" fill="currentColor">
                            <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2h-1V9z" clipRule="evenodd" />
                        </svg>
                    </div>
                    <div className="ml-3">
                        <p className="text-sm text-blue-700">No support logs found for this driver.</p>
                    </div>
                </div>
            </div>
        );
    }

    return (
        <div className="tabled">
            <div className="overflow-hidden bg-white dark:border-white/[0.05] dark:bg-white/[0.03] border-t-0 rounded-b-[12px]">
                <div className="header-bar flex items-center justify-between p-3 bg-tables dark:bg-gray-800">
                    {/* Search Bar */}
                    <form className="flex-1 max-w-md">
                        <label className="mb-2 text-sm font-medium text-gray-900 sr-only dark:text-white">
                            Search
                        </label>
                        {/* input can go here */}
                    </form>

                    {/* Buttons Container */}
                    <div className="flex items-center gap-2">
                        {/* Filter Button */}
                        <button
                            type="button"
                            className="flex items-center gap-2 py-2 px-3 text-xs font-medium text-[#76787A] focus:outline-none bg-white rounded-full border border-gray-200 hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-4 focus:ring-gray-100 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700"
                        >
                            <svg
                                xmlns="http://www.w3.org/2000/svg"
                                fill="none"
                                viewBox="0 0 24 24"
                                strokeWidth="1.5"
                                stroke="currentColor"
                                className="w-4 h-4"
                            >
                                <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    d="M12 3c2.755 0 5.455.232 8.083.678.533.09.917.556.917 1.096v1.044a2.25 2.25 0 0 1-.659 1.591l-5.432 5.432a2.25 2.25 0 0 0-.659 1.591v2.927a2.25 2.25 0 0 1-1.244 2.013L9.75 21v-6.568a2.25 2.25 0 0 0-.659-1.591L3.659 7.409A2.25 2.25 0 0 1 3 5.818V4.774c0-.54.384-1.006.917-1.096A48.32 48.32 0 0 1 12 3Z"
                                />
                            </svg>
                            Filters
                        </button>

                        {/* Refresh Button */}
                        <button
                            type="button"
                            aria-label="refresh"
                            className="flex items-center justify-center p-1 text-[#76787A] focus:outline-none bg-white rounded-full border border-gray-200 hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-4 focus:ring-gray-100 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700"
                        >
                            <PiClockCounterClockwiseLight size={22} />
                        </button>

                        {/* Download Button */}
                        <button
                            type="button"
                            aria-label="download"
                            className="flex items-center justify-center p-2 text-[#76787A] focus:outline-none bg-white rounded-full border border-gray-200 hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-4 focus:ring-gray-100 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700"
                        >
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"
                                className="size-4">
                                <path fillRule="evenodd"
                                    d="M12 2.25a.75.75 0 0 1 .75.75v11.69l3.22-3.22a.75.75 0 1 1 1.06 1.06l-4.5 4.5a.75.75 0 0 1-1.06 0l-4.5-4.5a.75.75 0 1 1 1.06-1.06l3.22 3.22V3a.75.75 0 0 1 .75-.75Zm-9 13.5a.75.75 0 0 1 .75.75v2.25a1.5 1.5 0 0 0 1.5 1.5h13.5a1.5 1.5 0 0 0 1.5-1.5V16.5a.75.75 0 0 1 1.5 0v2.25a3 3 0 0 1-3 3H5.25a3 3 0 0 1-3-3V16.5a.75.75 0 0 1 .75-.75Z"
                                    clip-rule="evenodd" />
                            </svg>
                            {/* <FaDownload size={20} /> */}
                        </button>
                    </div>
                </div>
                <div className="max-w-full overflow-x-auto custom-scrollbar">
                    <div className="max-w-[900px] min-w-[-webkit-fill-available]">
                        <Table>
                            <TableHeader className="border-b bg-tables dark:border-white/[0.05">
                                <TableRow className="border-t">
                                    <TableCell isHeader className="px-4 py-3 w-4">
                                        <input type="checkbox" 
                                        className='form-checkbox h-4 w-4 text-blue-600 rounded'/>
                                    </TableCell>

                                    <TableCell
                                        isHeader
                                        className="px-4 py-3 font-medium text-gray-500 text-start text-[12px] whitespace-nowrap select-none"
                                    >
                                        <div
                                            onClick={() => handleSort('created_at')}
                                            className="cursor-pointer flex items-center"
                                            role="button"
                                            tabIndex={0}
                                            onKeyPress={(e) => {
                                                if (e.key === 'Enter' || e.key === ' ') handleSort('created_at');
                                            }}
                                        >
                                            Date/Time
                                            <SortIcon
                                                direction={sortConfig?.key === 'created_at' ? sortConfig.direction : 'asc'}
                                                active={sortConfig?.key === 'created_at'}
                                            />
                                        </div>
                                    </TableCell>

                                    <TableCell
                                        isHeader
                                        className="px-4 py-3 font-medium text-gray-500 text-start text-[12px] whitespace-nowrap select-none"
                                    >
                                        <div
                                            onClick={() => handleSort('issue_id')}
                                            className="cursor-pointer flex items-center"
                                            role="button"
                                            tabIndex={0}
                                            onKeyPress={(e) => {
                                                if (e.key === 'Enter' || e.key === ' ') handleSort('issue_id');
                                            }}
                                        >
                                            Issue ID
                                            <SortIcon
                                                direction={sortConfig?.key === 'issue_id' ? sortConfig.direction : 'asc'}
                                                active={sortConfig?.key === 'issue_id'}
                                            />
                                        </div>
                                    </TableCell>

                                    <TableCell
                                        isHeader
                                        className="px-4 py-3 font-medium text-gray-500 text-start text-[12px] whitespace-nowrap select-none"
                                    >
                                        <div
                                            onClick={() => handleSort('issue_type')}
                                            className="cursor-pointer flex items-center"
                                            role="button"
                                            tabIndex={0}
                                            onKeyPress={(e) => {
                                                if (e.key === 'Enter' || e.key === ' ') handleSort('issue_type');
                                            }}
                                        >
                                            Issue Type
                                            <SortIcon
                                                direction={sortConfig?.key === 'issue_type' ? sortConfig.direction : 'asc'}
                                                active={sortConfig?.key === 'issue_type'}
                                            />
                                        </div>
                                    </TableCell>

                                    <TableCell isHeader className="px-4 py-3 font-medium text-gray-500 text-start text-[12px] whitespace-nowrap">
                                        Issue Description
                                    </TableCell>

                                    <TableCell
                                        isHeader
                                        className="px-4 py-3 font-medium text-gray-500 text-start text-[12px] whitespace-nowrap select-none"
                                    >
                                        <div
                                            onClick={() => handleSort('priority')}
                                            className="cursor-pointer flex items-center"
                                            role="button"
                                            tabIndex={0}
                                            onKeyPress={(e) => {
                                                if (e.key === 'Enter' || e.key === ' ') handleSort('priority');
                                            }}
                                        >
                                            Priority
                                            <SortIcon
                                                direction={sortConfig?.key === 'priority' ? sortConfig.direction : 'asc'}
                                                active={sortConfig?.key === 'priority'}
                                            />
                                        </div>
                                    </TableCell>

                                    <TableCell
                                        isHeader
                                        className="px-4 py-3 font-medium text-gray-500 text-start text-[12px] whitespace-nowrap select-none"
                                    >
                                        <div
                                            onClick={() => handleSort('status')}
                                            className="cursor-pointer flex items-center"
                                            role="button"
                                            tabIndex={0}
                                            onKeyPress={(e) => {
                                                if (e.key === 'Enter' || e.key === ' ') handleSort('status');
                                            }}
                                        >
                                            Status
                                            <SortIcon
                                                direction={sortConfig?.key === 'status' ? sortConfig.direction : 'asc'}
                                                active={sortConfig?.key === 'status'}
                                            />
                                        </div>
                                    </TableCell>

                                    <TableCell
                                        isHeader
                                        className="px-4 py-3 font-medium text-gray-500 text-start text-[12px] whitespace-nowrap cursor-pointer select-none"
                                    >
                                        <div
                                            onClick={() => handleSort('resolution_date')}
                                            className="cursor-pointer flex items-center"
                                            role="button"
                                            tabIndex={0}
                                            onKeyPress={(e) => {
                                                if (e.key === 'Enter' || e.key === ' ') handleSort('resolution_date');
                                            }}
                                        >
                                            Resolution Date
                                            <SortIcon
                                                direction={sortConfig?.key === 'resolution_date' ? sortConfig.direction : 'asc'}
                                                active={sortConfig?.key === 'resolution_date'}
                                            />
                                        </div>
                                    </TableCell>

                                    <TableCell
                                        isHeader
                                        className="px-4 py-3 font-medium text-gray-500 text-start text-[12px] whitespace-nowrap cursor-pointer select-none"
                                    >
                                        <div
                                            onClick={() => handleSort('assigned_admin')}
                                            className="cursor-pointer flex items-center"
                                            role="button"
                                            tabIndex={0}
                                            onKeyPress={(e) => {
                                                if (e.key === 'Enter' || e.key === ' ') handleSort('assigned_admin');
                                            }}
                                        >
                                            Assigned Admin
                                            <SortIcon
                                                direction={sortConfig?.key === 'assigned_admin' ? sortConfig.direction : 'asc'}
                                                active={sortConfig?.key === 'assigned_admin'}
                                            />
                                        </div>
                                    </TableCell>

                                    <TableCell isHeader className="px-4 py-3 font-medium text-gray-500 text-start text-[12px] whitespace-nowrap" children={''} />
                                </TableRow>
                            </TableHeader>


                            <TableBody className="divide-y dark:divide-white/[0.05]">
                                {sortedLogs.map((log) => (
                                    <TableRow key={log.id} className="cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-800 bg-tables">
                                        <TableCell className="px-4 py-3 w-4">
                                            <input type="checkbox" 
                                            className='form-checkbox h-4 w-4 text-blue-600 rounded'/>
                                        </TableCell>

                                        <TableCell className="px-4 py-3 whitespace-nowrap">
                                            <p className="text-[#050013] text-[12px]">{formatDateTime(log.created_at)}</p>
                                        </TableCell>
                                        <TableCell className="px-4 py-3 whitespace-nowrap">
                                            <p className="text-[#050013] text-[12px] font-medium">{log.issue_id}</p>
                                        </TableCell>
                                        <TableCell className="px-4 py-3 whitespace-nowrap">
                                            <p className="text-[#050013] text-[12px]">{log.issue_type}</p>
                                        </TableCell>
                                        <TableCell className="px-4 py-3 max-w-[210px] whitespace-nowrap">
                                            <p className="text-[#050013] text-[12px] line-clamp-2">{log.issue_description}</p>
                                        </TableCell>
                                        <TableCell className="px-4 py-3 whitespace-nowrap">
                                            <Badge size="sm" color={getPriorityColor(log.priority)}>{log.priority}</Badge>
                                        </TableCell>
                                        {/* <TableCell className="px-4 py-3 whitespace-nowrap">
                                            <div className="flex items-center gap-1">
                                                <span className={`inline-block w-2 h-2 rounded-full ${log.status === 'resolved' ? 'bg-green-500' : 'bg-yellow-500'}`}></span>
                                                <span className="text-[#050013] text-[12px] capitalize">{log.status}</span>
                                            </div>
                                        </TableCell> */}
                                     <TableCell className="px-4 py-3 whitespace-nowrap">
                                        <div className="flex items-center gap-1">
                                            <span className={`inline-block w-2 h-2 rounded-full ${log.status === 'resolved' ? 'bg-green-500' : 'bg-yellow-500'}`}></span>
                                            <span className={`text-[12px] capitalize ${log.status === 'resolved' ? 'text-green-600' : 'text-yellow-600'}`}>
                                              {log.status}
                                             </span>
                                        </div>
                                        </TableCell>


                                        <TableCell className="px-4 py-3 whitespace-nowrap">
                                            <p className="text-[#050013] text-[12px]">{log.resolution_date ? formatDateTime(log.resolution_date) : 'N/A'}</p>
                                        </TableCell>
                                        <TableCell className="px-4 py-3 whitespace-nowrap">
                                            <p className="text-[#050013] text-[12px]">{log.assigned_admin}</p>
                                        </TableCell>

                                        <TableCell className="px-4 text-gray-500 text-theme-sm dark:text-gray-400">
                                            <Tippy
                                                content={
                                                    <div className="bg-white text-gray-900">
                                                        <div className="flex flex-col space-y-1 p-1">
                                                            <button className="text-[#76787A] hover:text-[#050013] px-2 py-1 rounded w-full text-start">
                                                                View Details
                                                            </button>
                                                            <button className="text-[#76787A] hover:text-[#050013] px-2 py-1 rounded w-full text-start">
                                                                {log.status === 'resolved' ? 'Reopen Issue' : 'Resolve Issue'}
                                                            </button>
                                                            <button className="text-[#76787A] hover:text-[#050013] px-2 py-1 rounded w-full text-start">
                                                                Add Comment
                                                            </button>
                                                            <button className="text-[#76787A] hover:text-[#050013] px-2 py-1 rounded w-full text-start">
                                                                Download Log
                                                            </button>
                                                        </div>
                                                    </div>
                                                }
                                                interactive={true}
                                                placement="left"
                                                theme="light"
                                                arrow={false}
                                                duration={0}
                                                className="!bg-white !text-gray-900 border border-gray-200 rounded-lg shadow-sm"
                                            >
                                                <button type="button" className="focus:outline-none" aria-label="actions">
                                                    <BsThreeDotsVertical />

                                                </button>
                                            </Tippy>
                                        </TableCell>

                                    </TableRow>
                                ))}
                            </TableBody>
                        </Table>

                    </div>
                </div>
            </div>
        </div>
    );
}