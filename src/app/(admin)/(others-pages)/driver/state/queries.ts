import { backendApiClient } from "@/utils/apiClient";
export async function getTableDriverDetails() {
    const rawResponse = await backendApiClient
        .get("driver-management/driver/get-drivers",
        ).json().then((response) => response);
    return rawResponse;
}

export async function addTableDriverDetails(payload: any) {
    const rawResponse = await backendApiClient
        .post("driver-management/driver/create-driver",
            { json: payload }
        ).json();
    return rawResponse;
}
export async function getSpecificDriverDetails(id: string) {
    const rawResponse: any = await backendApiClient
        .get(`driver-management/driver/get-driver/${id}`,
        ).json().then((response) => response);
    return rawResponse;
}

export const deleteDriver = (payload: string) =>
    backendApiClient
        .delete(`driver-management/driver/delete-driver/${payload}`,)
        .json();

export const disableDriverQuery = (payload: any) =>
    backendApiClient
        .patch(`driver-management/driver/change-driver-status/${payload?.driverId}`, {
            json: { driverStatus: payload?.status },
        })
        .json();

export const downloadReport = (payload: string,) =>
    backendApiClient
        .get(`driver-management/driver/export-driver/csv/${payload}`,)
        .blob();

export const editDriverDetails = (payload: FormData, id: string) =>
    backendApiClient
        .put(`driver-management/driver/update-driver/${id}`, {
            body: payload,
        })
        .json();

export const fetchDriverSupportLogs = (driverId: string) => {

    // return backendApiClient
    //     .get(`driver-support/driver/${driverId}`)
    //     .json();

    return [
        {
            id: 1,
            issue_id: "ISSUE001",
            issue_type: "Payment",
            issue_description: "Earnings not updated after trip.",
            priority: "High",
            status: "open",
            resolution_date: null,
            assigned_admin: "Admin A",
            created_at: "2025-06-20T09:15:00.000Z",
            updated_at: "2025-06-20T09:15:00.000Z",
            driver_id: 18,
            driverName: "John Doe",
            driverEmail: "<EMAIL>",
            driverPhone: "1234567890",
            driverImage: "https://randomuser.me/api/portraits/men/1.jpg",
            driverStatus: "Active",
            driverRegion: "North"
        },
        {
            id: 2,
            issue_id: "ISSUE002",
            issue_type: "Technical",
            issue_description: "App crashes when starting a trip.",
            priority: "Medium",
            status: "resolved",
            resolution_date: "2025-06-22T14:30:00.000Z",
            assigned_admin: "Admin B",
            created_at: "2025-06-19T13:45:00.000Z",
            updated_at: "2025-06-22T14:30:00.000Z",
            driver_id: 19,
            driverName: "Alice Smith",
            driverEmail: "<EMAIL>",
            driverPhone: "2345678901",
            driverImage: "https://randomuser.me/api/portraits/women/2.jpg",
            driverStatus: "Inactive",
            driverRegion: "East"
        },
        {
            id: 3,
            issue_id: "ISSUE003",
            issue_type: "Navigation",
            issue_description: "Wrong route suggested by app.",
            priority: "Low",
            status: "pending",
            resolution_date: null,
            assigned_admin: "Admin C",
            created_at: "2025-06-18T10:05:00.000Z",
            updated_at: "2025-06-18T10:05:00.000Z",
            driver_id: 20,
            driverName: "Bob Johnson",
            driverEmail: "<EMAIL>",
            driverPhone: "**********",
            driverImage: "https://randomuser.me/api/portraits/men/3.jpg",
            driverStatus: "Active",
            driverRegion: "West"
        },
        {
            id: 4,
            issue_id: "ISSUE004",
            issue_type: "Account",
            issue_description: "Unable to reset password.",
            priority: "Medium",
            status: "resolved",
            resolution_date: "2025-06-23T08:00:00.000Z",
            assigned_admin: "Admin D",
            created_at: "2025-06-17T07:00:00.000Z",
            updated_at: "2025-06-23T08:00:00.000Z",
            driver_id: 21,
            driverName: "Carol White",
            driverEmail: "<EMAIL>",
            driverPhone: "**********",
            driverImage: "https://randomuser.me/api/portraits/women/4.jpg",
            driverStatus: "Suspended",
            driverRegion: "South"
        },
        {
            id: 5,
            issue_id: "ISSUE005",
            issue_type: "Payment",
            issue_description: "Trip bonus missing from earnings.",
            priority: "High",
            status: "open",
            resolution_date: null,
            assigned_admin: null,
            created_at: "2025-06-21T11:25:51.385Z",
            updated_at: "2025-06-21T11:25:51.385Z",
            driver_id: 22,
            driverName: "David Lee",
            driverEmail: "<EMAIL>",
            driverPhone: "5678901234",
            driverImage: "https://randomuser.me/api/portraits/men/5.jpg",
            driverStatus: "Active",
            driverRegion: "Central"
        },
        {
            id: 6,
            issue_id: "ISSUE006",
            issue_type: "Support",
            issue_description: "No response from support for over 2 days.",
            priority: "Medium",
            status: "pending",
            resolution_date: null,
            assigned_admin: "Admin E",
            created_at: "2025-06-16T11:00:00.000Z",
            updated_at: "2025-06-16T11:00:00.000Z",
            driver_id: 23,
            driverName: "Eva Green",
            driverEmail: "<EMAIL>",
            driverPhone: "6789012345",
            driverImage: "https://randomuser.me/api/portraits/women/6.jpg",
            driverStatus: "Inactive",
            driverRegion: "East"
        },
        {
            id: 7,
            issue_id: "ISSUE007",
            issue_type: "Payment",
            issue_description: "Incorrect payout calculation.",
            priority: "High",
            status: "resolved",
            resolution_date: "2025-06-24T10:10:00.000Z",
            assigned_admin: "Admin F",
            created_at: "2025-06-15T10:00:00.000Z",
            updated_at: "2025-06-24T10:10:00.000Z",
            driver_id: 24,
            driverName: "Frank Moore",
            driverEmail: "<EMAIL>",
            driverPhone: "7890123456",
            driverImage: "https://randomuser.me/api/portraits/men/7.jpg",
            driverStatus: "Active",
            driverRegion: "North"
        },
        {
            id: 8,
            issue_id: "ISSUE008",
            issue_type: "Technical",
            issue_description: "App not loading on Android.",
            priority: "Low",
            status: "open",
            resolution_date: null,
            assigned_admin: null,
            created_at: "2025-06-14T12:30:00.000Z",
            updated_at: "2025-06-14T12:30:00.000Z",
            driver_id: 25,
            driverName: "Grace Kim",
            driverEmail: "<EMAIL>",
            driverPhone: "**********",
            driverImage: "https://randomuser.me/api/portraits/women/8.jpg",
            driverStatus: "Active",
            driverRegion: "West"
        },
        {
            id: 9,
            issue_id: "ISSUE009",
            issue_type: "Account",
            issue_description: "Driver profile picture not uploading.",
            priority: "Medium",
            status: "pending",
            resolution_date: null,
            assigned_admin: "Admin G",
            created_at: "2025-06-13T16:00:00.000Z",
            updated_at: "2025-06-13T16:00:00.000Z",
            driver_id: 26,
            driverName: "Hannah Ray",
            driverEmail: "<EMAIL>",
            driverPhone: "**********",
            driverImage: "https://randomuser.me/api/portraits/women/9.jpg",
            driverStatus: "Suspended",
            driverRegion: "Central"
        },
        {
            id: 10,
            issue_id: "ISSUE010",
            issue_type: "Support",
            issue_description: "Unable to reach driver support chat.",
            priority: "Low",
            status: "resolved",
            resolution_date: "2025-06-25T09:00:00.000Z",
            assigned_admin: "Admin H",
            created_at: "2025-06-12T15:00:00.000Z",
            updated_at: "2025-06-25T09:00:00.000Z",
            driver_id: 27,
            driverName: "Ian Ford",
            driverEmail: "<EMAIL>",
            driverPhone: "0123456789",
            driverImage: "https://randomuser.me/api/portraits/men/10.jpg",
            driverStatus: "Active",
            driverRegion: "South"
        }
    ];
};


export const fetchDriverActivityLogs = (driverId: string) => {
    // return backendApiClient
    //     .get(`driver-activity/${driverId}`)
    //     .json();

    return [
        {
            "tripId": 1,
            "dateTime": "2025-06-01T08:30:00.000Z",
            "startLocation": "Start Point A",
            "endLocation": "End Point A",
            "status": "completed",
            "distance": 12.5,
            "duration": 45.2,
            "earnings": 132.5,
            "rating": 4.8,
            "feedback": "Very smooth ride.",
            "driver": {
                "id": 10,
                "name": "Alice Smith",
                "email": "<EMAIL>"
            }
        },
        {
            "tripId": 2,
            "dateTime": "2025-06-02T10:15:30.000Z",
            "startLocation": "Start Point B",
            "endLocation": "End Point B",
            "status": "cancelled",
            "distance": 0,
            "duration": 0,
            "earnings": 0,
            "rating": null,
            "feedback": "Rider cancelled.",
            "driver": {
                "id": 11,
                "name": "Bob Johnson",
                "email": "<EMAIL>"
            }
        },
        {
            "tripId": 3,
            "dateTime": "2025-06-03T14:45:00.000Z",
            "startLocation": "Start Point C",
            "endLocation": "End Point C",
            "status": "completed",
            "distance": 8.3,
            "duration": 38.6,
            "earnings": 98.0,
            "rating": 4.2,
            "feedback": "Good driver.",
            "driver": {
                "id": 12,
                "name": "Charlie Lee",
                "email": "<EMAIL>"
            }
        },
        {
            "tripId": 4,
            "dateTime": "2025-06-04T18:20:00.000Z",
            "startLocation": "Start Point D",
            "endLocation": "End Point D",
            "status": "completed",
            "distance": 15.6,
            "duration": 52.0,
            "earnings": 160.0,
            "rating": 5.0,
            "feedback": "Excellent service!",
            "driver": {
                "id": 13,
                "name": "Diana Prince",
                "email": "<EMAIL>"
            }
        },
        {
            "tripId": 5,
            "dateTime": "2025-06-05T09:10:00.000Z",
            "startLocation": "Start Point E",
            "endLocation": "End Point E",
            "status": "no-show",
            "distance": 0,
            "duration": 0,
            "earnings": 0,
            "rating": null,
            "feedback": "Rider did not show up.",
            "driver": {
                "id": 14,
                "name": "Evan Wright",
                "email": "<EMAIL>"
            }
        },
        {
            "tripId": 6,
            "dateTime": "2025-06-06T07:25:00.000Z",
            "startLocation": "Start Point F",
            "endLocation": "End Point F",
            "status": "completed",
            "distance": 6.9,
            "duration": 28.5,
            "earnings": 85.4,
            "rating": 3.9,
            "feedback": "Traffic was a bit heavy.",
            "driver": {
                "id": 15,
                "name": "Fiona Hall",
                "email": "<EMAIL>"
            }
        },
        {
            "tripId": 7,
            "dateTime": "2025-06-07T13:55:00.000Z",
            "startLocation": "Start Point G",
            "endLocation": "End Point G",
            "status": "completed",
            "distance": 11.3,
            "duration": 41.0,
            "earnings": 120.3,
            "rating": 4.5,
            "feedback": "Pleasant experience.",
            "driver": {
                "id": 16,
                "name": "George King",
                "email": "<EMAIL>"
            }
        },
        {
            "tripId": 8,
            "dateTime": "2025-06-08T12:00:00.000Z",
            "startLocation": "Start Point H",
            "endLocation": "End Point H",
            "status": "completed",
            "distance": 9.7,
            "duration": 36.8,
            "earnings": 110.9,
            "rating": 4.0,
            "feedback": "All good.",
            "driver": {
                "id": 17,
                "name": "Hannah Brown",
                "email": "<EMAIL>"
            }
        },
        {
            "tripId": 9,
            "dateTime": "2025-06-09T17:40:00.000Z",
            "startLocation": "Start Point I",
            "endLocation": "End Point I",
            "status": "completed",
            "distance": 13.2,
            "duration": 50.2,
            "earnings": 145.6,
            "rating": 4.7,
            "feedback": "Driver was very professional.",
            "driver": {
                "id": 18,
                "name": "Isaac Clarke",
                "email": "<EMAIL>"
            }
        },
        {
            "tripId": 10,
            "dateTime": "2025-06-10T06:19:57.212Z",
            "startLocation": "Start Point J",
            "endLocation": "End Point J",
            "status": "completed",
            "distance": 10.26,
            "duration": 64.1,
            "earnings": 147.8,
            "rating": 0.5,
            "feedback": "Feedback message #10",
            "driver": {
                "id": 19,
                "name": "Jack Daniels",
                "email": "<EMAIL>"
            }
        }
    ]

};

export const fetchDriverEarnings = (driverId: string) => {
    // return backendApiClient
    //     .get(`driver-activity/earnings/${driverId}`)
    //     .json();

    return [
        {
            tripId: "#10001",
            dateTime: "2025-06-20T10:15:30.000Z",
            baseFare: 15.75,
            bonuses: 2.50,
            penalties: 1.00,
            tips: 3.25,
            totalFare: 20.50,
            paymentType: "Cash",
            companyEarnings: "5.00 (25%)",
            driverEarnings: "15.50 (75%)",
            paymentStatus: "Completed",
            payoutDate: "2025-06-22"
        },
        {
            tripId: "#10002",
            dateTime: "2025-06-21T14:12:55.681Z",
            baseFare: 19.47,
            bonuses: 3.45,
            penalties: 0.98,
            tips: 3.41,
            totalFare: 25.35,
            paymentType: "Card",
            companyEarnings: "6.33 (25%)",
            driverEarnings: "19.02 (75%)",
            paymentStatus: "Completed",
            payoutDate: "2025-06-23"
        },
        {
            tripId: "#10003",
            dateTime: "2025-06-22T09:05:10.000Z",
            baseFare: 12.00,
            bonuses: 1.00,
            penalties: 0,
            tips: 0.50,
            totalFare: 13.50,
            paymentType: "Wallet",
            companyEarnings: "3.38 (25%)",
            driverEarnings: "10.13 (75%)",
            paymentStatus: "Pending",
            payoutDate: "2025-06-24"
        },
        {
            tripId: "#10004",
            dateTime: "2025-06-23T18:20:45.000Z",
            baseFare: 22.00,
            bonuses: 2.00,
            penalties: 1.50,
            tips: 4.00,
            totalFare: 26.50,
            paymentType: "Card",
            companyEarnings: "6.63 (25%)",
            driverEarnings: "19.88 (75%)",
            paymentStatus: "Completed",
            payoutDate: "2025-06-25"
        },
        {
            tripId: "#10005",
            dateTime: "2025-06-24T16:45:00.000Z",
            baseFare: 18.00,
            bonuses: 1.50,
            penalties: 0,
            tips: 2.00,
            totalFare: 21.50,
            paymentType: "Cash",
            companyEarnings: "5.38 (25%)",
            driverEarnings: "16.13 (75%)",
            paymentStatus: "Failed",
            payoutDate: "2025-06-26"
        },
        {
            tripId: "#10006",
            dateTime: "2025-06-25T08:30:00.000Z",
            baseFare: 20.00,
            bonuses: 0,
            penalties: 2.00,
            tips: 3.00,
            totalFare: 21.00,
            paymentType: "Card",
            companyEarnings: "5.25 (25%)",
            driverEarnings: "15.75 (75%)",
            paymentStatus: "Completed",
            payoutDate: "2025-06-27"
        },
        {
            tripId: "#10007",
            dateTime: "2025-06-25T11:00:00.000Z",
            baseFare: 25.00,
            bonuses: 4.00,
            penalties: 1.00,
            tips: 5.00,
            totalFare: 33.00,
            paymentType: "Wallet",
            companyEarnings: "8.25 (25%)",
            driverEarnings: "24.75 (75%)",
            paymentStatus: "Completed",
            payoutDate: "2025-06-27"
        },
        {
            tripId: "#10008",
            dateTime: "2025-06-26T09:30:00.000Z",
            baseFare: 14.00,
            bonuses: 1.00,
            penalties: 0.50,
            tips: 1.50,
            totalFare: 16.00,
            paymentType: "Cash",
            companyEarnings: "4.00 (25%)",
            driverEarnings: "12.00 (75%)",
            paymentStatus: "Pending",
            payoutDate: "2025-06-28"
        },
        {
            tripId: "#10009",
            dateTime: "2025-06-26T13:45:00.000Z",
            baseFare: 10.00,
            bonuses: 0.50,
            penalties: 0.20,
            tips: 2.00,
            totalFare: 12.30,
            paymentType: "Card",
            companyEarnings: "3.08 (25%)",
            driverEarnings: "9.23 (75%)",
            paymentStatus: "Completed",
            payoutDate: "2025-06-28"
        },
        {
            tripId: "#10010",
            dateTime: "2025-06-26T17:00:00.000Z",
            baseFare: 30.00,
            bonuses: 5.00,
            penalties: 2.00,
            tips: 6.00,
            totalFare: 39.00,
            paymentType: "Wallet",
            companyEarnings: "9.75 (25%)",
            driverEarnings: "29.25 (75%)",
            paymentStatus: "Completed",
            payoutDate: "2025-06-29"
        }
    ];

}   