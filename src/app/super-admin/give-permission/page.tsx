"use client";

import React, { useState } from "react";
import { FaChevronDown } from "react-icons/fa";

const permissionData = [
  {
    title: "User & Role Management",
    permissions: [
      { label: "Create, edit, delete , Dispatchers, Drivers, Users", checked: true },
      { label: "Assign Admins to taxi companies", checked: true },
      { label: "Manage permissions for all roles", checked: true },
      { label: "Suspend/reactivate any user", checked: false, isDisabled: true },
    ],
  },
  {
    title: "Booking & Dispatch Control",
    permissions: [
      { label: "View, edit, and manage all bookings", checked: true },
      { label: "Assign/reassign drivers to rides", checked: true },
      { label: "View driver live locations", checked: true },
      { label: "Cancel or reschedule any ride", checked: false, isDisabled: true },
      { label: "View driver live locations", checked: false, isDisabled: true },
    ],
  },
  {
    title: "Company & Financial Management",
    permissions: [
      { label: "Add, edit, or remove taxi companies", checked: true },
      { label: "Manage company subscription plans", checked: true },
      { label: "View & modify company-specific pricing and fare structures", checked: true },
      { label: "Access & edit company billing information", checked: false, isDisabled: true },
    ],
  },
  {
    title: "System & Policy Settings",
    permissions: [
      { label: "Define platform-wide fare policies", checked: true },
      { label: "Set geofencing rules & restrictions", checked: true },
      { label: "Control global discount and promo policies", checked: true },
      { label: "Configure ride cancellation policies", checked: false, isDisabled: true },
    ],
  },
  {
    title: "Reporting & Analytics",
    permissions: [
      { label: "View and export reports on revenue, ride activity, and system performance", checked: true },
      { label: "Monitor driver performance & customer ratings", checked: true },
      { label: "Analyze dispatcher efficiency", checked: true },
    ],
  },
];

export default function NewClient() {
  const [openStep, setOpenStep] = useState(null);
  const [checked, setChecked] = useState(() => {
    const initial = {};
    permissionData.forEach((section, i) => {
      section.permissions.forEach((perm, j) => {
        initial[`${i}-${j}`] = perm.checked;
      });
    });
    return initial;
  });

  const toggleStep = (step) => {
    setOpenStep(openStep === step ? null : step);
  };

  const toggleCheckbox = (key) => {
    setChecked((prev) => ({
      ...prev,
      [key]: !prev[key],
    }));
  };

  return (
    <div className="p-4 sm:p-6 md:p-8 mx-auto border border-[#e8e8e8] rounded-[15px] shadow-sm overflow-hidden">
      <div className="flex items-center gap-3 mb-6">
        <img
          src="/images/give-permission.svg"
          alt="Icon"
          className="w-12 h-12 object-contain"
        />
        <h1 className="text-[20px] font-medium text-[#050013] text-center sm:text-left">
          Give Permissions
        </h1>
      </div>

      {/* Accordion List */}
      {permissionData.map((section, i) => (
        <div key={i} className="py-2">
          <button
            onClick={() => toggleStep(i)}
            className="w-full flex justify-between items-center px-4 py-3 bg-tables hover:bg-gray-200 transition"
          >
            <h2 className="text-[14px] font-medium text-[#050013]">{section.title}</h2>
            <FaChevronDown
              className={`text-[#76787A] transition-transform duration-300 ${
                openStep === i ? "rotate-180" : ""
              }`}
            />
          </button>

          {openStep === i && (
            <div className="mt-2 space-y-2 pl-3">
              {section.permissions.map((perm, j) => {
                const key = `${i}-${j}`;
                const isChecked = checked[key];
                return (
                  <label
                    key={key}
                    className={`flex items-start gap-2 text-[14px] text-normal text-[#76787A] peer cursor-pointer
                        ? "cursor-pointer"
                        : "text-[#050013]"
                    }`}
                  >
                    <input
                      type="checkbox"
                      className="peer mt-1 accent-[#3324E3]"
                      checked={isChecked}
                      onChange={() => toggleCheckbox(key)}
                    />
                    <span
                      className={`peer-checked:text-[#050013]`}
                    >
                      {perm.label}
                    </span>
                  </label>
                );
              })}
            </div>
          )}
        </div>
      ))}

      {/* Continue Button */}
      <div className="mt-8 text-center sm:text-right">
        <button className="bg-[#3324E3] text-white font-medium px-6 py-2 rounded-full hover:bg-[#291ed3] transition">
          Add
        </button>
      </div>
    </div>
  );
}
