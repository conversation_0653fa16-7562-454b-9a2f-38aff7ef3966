"use client";

import React, { useState } from "react";
import { FaChevronDown } from "react-icons/fa";
import { FiUser, FiPhone } from "react-icons/fi";
import Link from "next/link";

export default function NewClient() {
  const [openStep, setOpenStep] = useState(null);

  const toggleStep = (step) => {
    setOpenStep(openStep === step ? null : step);
  };

  return (
    <div className="p-4 sm:p-6 md:p-8 mx-auto border border-[#e8e8e8] rounded-[15px] shadow-sm overflow-hidden">
      <div className="flex items-center gap-3 mb-6">
        <img src="/images/gen-info.svg" alt="Icon" className="w-12 h-12 object-contain" />
        <h1 className="text-[20px] font-medium text-[#050013] text-center sm:text-left">General Info</h1>
      </div>

      <div className="space-y-4">
        {/* Company Information */}
        <div className="rounded-sm overflow-hidden">
          <button
            onClick={() => toggleStep(0)}
            className="w-full flex justify-between items-center px-4 py-3 bg-tables hover:bg-gray-200 transition"
          >
            <h2 className="font-medium text-[14px] text-[#050013]">Company Information</h2>
            <FaChevronDown className={`transition-transform duration-200 ${openStep === 0 ? "rotate-180" : "rotate-0"}`} />
          </button>
          {openStep === 0 && (
            <div className="p-4 bg-white space-y-4">
             <div className="p-4 bg-white space-y-4">
  <input
    type="text"
    placeholder="Company Name"
    className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-[#3324E3]"
  />
  <input
    type="text"
    placeholder="Legal Name (if different)"
    className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-[#3324E3]"
  />
  <input
    type="text"
    placeholder="Company Registration Number"
    className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-[#3324E3]"
  />
  <input
    type="text"
    placeholder="Tax Identification Number (TIN/VAT)"
    className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-[#3324E3]"
  />
  <input
    type="text"
    placeholder="Business Type"
    className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-[#3324E3]"
  />
  <input
    type="url"
    placeholder="Website URL"
    className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-[#3324E3]"
  />
  <div className="mt-1">
  <p className="text-[14px] font-medium text-[#76787A] pb-3 dark:text-white">
  Upload a logo
  </p>

  <div className="shadow-none p-4 mt-1 bg-white border border-gray-200 rounded-lg dark:bg-gray-800 dark:border-gray-700">
    <div className="flex items-center justify-center w-full">
      <label className="flex flex-col items-center justify-center w-full border-2 border-gray-300 border-dashed rounded-lg cursor-pointer py-8">
        <div className="flex items-center justify-center pt-5 pb-6">
          <svg xmlns="http://www.w3.org/2000/svg" width="44" height="32" fill="none">
            <path
              fill="#76787A"
              d="M21.818 0c3.348 0 6.705 1.265 9.258 3.818 1.981 1.981 3.161 4.46 3.606 7.03 5.07.824 8.954 5.192 8.954 10.485C43.636 27.213 38.85 32 32.97 32H9.212A9.213 9.213 0 0 1 0 22.788c0-4.952 3.918-8.956 8.818-9.167-.142-3.528 1.07-7.115 3.758-9.803A13.02 13.02 0 0 1 21.818 0m0 1.94a11.08 11.08 0 0 0-7.879 3.257 11.13 11.13 0 0 0-3.181 9.227.97.97 0 0 1-.97 1.091h-.576a7.236 7.236 0 0 0-7.273 7.273 7.236 7.236 0 0 0 7.273 7.273H32.97a8.713 8.713 0 0 0 8.727-8.728 8.72 8.72 0 0 0-7.924-8.697.97.97 0 0 1-.879-.848c-.28-2.41-1.33-4.74-3.182-6.591a11.12 11.12 0 0 0-7.894-3.258m0 9.696c.26.005.513.116.652.243l5.333 4.848c.399.347.418 1.001.076 1.38-.342.377-1.003.403-1.379.06l-3.712-3.38v10.425a.97.97 0 1 1-1.94 0V14.788l-3.712 3.379c-.376.343-1.018.3-1.378-.06-.374-.375-.304-1.04.075-1.38l5.334-4.848c.213-.195.394-.243.651-.243"
            />
          </svg>
          <p className="text-sm text-dark-grey dark:text-gray-400">
            <span className="px-3 text-[14px]">Click or drag file to this area to upload</span>
          </p>
        </div>
        <input id="dropzone-file" className="hidden" type="file" name="driverLicenceFile" />
      </label>
    </div>
  </div>

  <p className="text-[#76787A] text-[13px] py-3">Formats accepted are PNG & JPG</p>
  <p className="text-[#050013] text-[13px] mt-2">If you do not have a file you can see the sample below</p>

  <div className="bg-tables p-3 flex justify-between items-center w-full mt-3" style={{ borderRadius: "10px" }}>
    <div>
      <p className="text-dark text-sm font-semibold mb-1">Sample Certificate</p>
      <p className="text-gray-400 text-sm">PNG 1.2MB</p>
    </div>
    <div className="flex items-center gap-2 text-blue-800 font-semibold">
      <svg
        stroke="currentColor"
        fill="currentColor"
        strokeWidth="0"
        viewBox="0 0 24 24"
        className="w-[20px] h-[20px]"
        height="1em"
        width="1em"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          fill="none"
          strokeWidth="2"
          d="M2.99787498,0.999999992 L17.4999998,0.999999992 L20.9999998,4.50000005 L21,23 L3,23 L2.99787498,0.999999992 Z M16,1 L16,6 L21,6 M12,9 L12,18 M8,15 L12,19 L16,15"
        />
      </svg>
      <p className="text-sm">Download</p>
    </div>
  </div>

  <div className="flex justify-end items-center gap-2 py-6">
    <button
      type="button"
      aria-label="Cancel"
      className="py-2.5 px-10 text-sm font-medium text-gray-900 focus:outline-none bg-white rounded-lg border border-gray-200 hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-4 focus:ring-gray-100 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700"
      style={{ borderRadius: "50px" }}
    >
      Cancel
    </button>
    <button
      type="button"
      aria-label="Upload"
      className="text-white bg-gray-800 hover:bg-gray-900 focus:outline-none focus:ring-4 focus:ring-gray-300 font-medium rounded-lg text-sm px-5 py-2.5 flex items-center gap-2 dark:bg-gray-800 dark:hover:bg-gray-700 dark:focus:ring-gray-700 dark:border-gray-700"
      style={{ borderRadius: "50px" }}
    >
      <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" className="size-4">
        <path strokeLinecap="round" strokeLinejoin="round" d="M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5m-13.5-9L12 3m0 0 4.5 4.5M12 3v13.5" />
      </svg>
      <span>Upload</span>
    </button>
  </div>
</div>

  <input
    placeholder="Short Company Description"
    className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-[#3324E3]"
  />

</div>
            </div>
          )}
        </div>

        {/* Contact Information */}
        <div className="rounded-sm overflow-hidden">
          <button
            onClick={() => toggleStep(1)}
            className="w-full flex justify-between items-center px-4 py-3 bg-tables hover:bg-gray-200 transition"
          >
            <h2 className="font-medium text-[14px] text-[#050013]">Contact Information</h2>
            <FaChevronDown className={`transition-transform duration-200 ${openStep === 1 ? "rotate-180" : "rotate-0"}`} />
          </button>
          {openStep === 1 && (
            <div className="p-4 bg-white space-y-4">
              <input
    type="text"
    placeholder="Primary Contact Person Name"
    className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-[#3324E3]"
  />
 <div className="relative w-full">
  <select
    defaultValue=""
    className="w-full appearance-none border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-[#3324E3] text-[#76787A]"
    onChange={(e) => {
      e.target.classList.remove("text-[#76787A]");
      e.target.classList.add("text-[#050013]");
    }}
  >
    <option value="" disabled>
    Designation
    </option>
    <option value="private">Private Limited</option>
    <option value="public">Public Limited</option>
    <option value="llp">LLP</option>
    <option value="prop">Proprietorship</option>
  </select>

  <div className="pointer-events-none absolute inset-y-0 right-3 flex items-center text-gray-500">
    <svg
      className="h-4 w-4"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      viewBox="0 0 24 24"
    >
      <path strokeLinecap="round" strokeLinejoin="round" d="M19 9l-7 7-7-7" />
    </svg>
  </div>
</div>


  <input
    type="text"
    placeholder="Email Address"
    className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-[#3324E3]"
  />
  <input
    type="text"
    placeholder="Phone Number"
    className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-[#3324E3]"
  />
   <input
    type="text"
    placeholder="Alternative Contact Person(optional)"
    className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-[#3324E3]"
  />
            </div>
          )}
        </div>

        {/* Business Details */}
        <div className="rounded-sm overflow-hidden">
          <button
            onClick={() => toggleStep(2)}
            className="w-full flex justify-between items-center px-4 py-3 bg-tables hover:bg-gray-200 transition"
          >
            <h2 className="font-medium text-[14px] text-[#050013]">Business Details</h2>
            <FaChevronDown className={`transition-transform duration-200 ${openStep === 2 ? "rotate-180" : "rotate-0"}`} />
          </button>
          {openStep === 2 && (
            <div className="p-4 bg-white space-y-4">
               <input
    type="text"
    placeholder="Head Office Address"
    className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-[#3324E3]"
  />
 <div className="relative w-full">
  <select
    defaultValue=""
    className="w-full appearance-none border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-[#3324E3] text-[#76787A]"
    onChange={(e) => {
      e.target.classList.remove("text-[#76787A]");
      e.target.classList.add("text-[#050013]");
    }}
  >
    <option value="" disabled>
    City
    </option>
    <option value="private">Private Limited</option>
    <option value="public">Public Limited</option>
    <option value="llp">LLP</option>
    <option value="prop">Proprietorship</option>
  </select>

  <div className="pointer-events-none absolute inset-y-0 right-3 flex items-center text-gray-500">
    <svg
      className="h-4 w-4"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      viewBox="0 0 24 24"
    >
      <path strokeLinecap="round" strokeLinejoin="round" d="M19 9l-7 7-7-7" />
    </svg>
  </div>
</div>
<div className="relative w-full">
  <select
    defaultValue=""
    className="w-full appearance-none border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-[#3324E3] text-[#76787A]"
    onChange={(e) => {
      e.target.classList.remove("text-[#76787A]");
      e.target.classList.add("text-[#050013]");
    }}
  >
    <option value="" disabled>
    State/Province
    </option>
    <option value="private">Private Limited</option>
    <option value="public">Public Limited</option>
    <option value="llp">LLP</option>
    <option value="prop">Proprietorship</option>
  </select>

  <div className="pointer-events-none absolute inset-y-0 right-3 flex items-center text-gray-500">
    <svg
      className="h-4 w-4"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      viewBox="0 0 24 24"
    >
      <path strokeLinecap="round" strokeLinejoin="round" d="M19 9l-7 7-7-7" />
    </svg>
  </div>
</div>
<div className="relative w-full">
  <select
    defaultValue=""
    className="w-full appearance-none border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-[#3324E3] text-[#76787A]"
    onChange={(e) => {
      e.target.classList.remove("text-[#76787A]");
      e.target.classList.add("text-[#050013]");
    }}
  >
    <option value="" disabled>
    Country
    </option>
    <option value="private">Private Limited</option>
    <option value="public">Public Limited</option>
    <option value="llp">LLP</option>
    <option value="prop">Proprietorship</option>
  </select>

  <div className="pointer-events-none absolute inset-y-0 right-3 flex items-center text-gray-500">
    <svg
      className="h-4 w-4"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      viewBox="0 0 24 24"
    >
      <path strokeLinecap="round" strokeLinejoin="round" d="M19 9l-7 7-7-7" />
    </svg>
  </div>
</div>


<input
    type="text"
    placeholder="Postal Code"
    className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-[#3324E3]"
  />

<div className="relative w-full">
  <select
    defaultValue=""
    className="w-full appearance-none border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-[#3324E3] text-[#76787A]"
    onChange={(e) => {
      e.target.classList.remove("text-[#76787A]");
      e.target.classList.add("text-[#050013]");
    }}
  >
    <option value="" disabled>
    Branch Location
    </option>
    <option value="private">Private Limited</option>
    <option value="public">Public Limited</option>
    <option value="llp">LLP</option>
    <option value="prop">Proprietorship</option>
  </select>

  <div className="pointer-events-none absolute inset-y-0 right-3 flex items-center text-gray-500">
    <svg
      className="h-4 w-4"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      viewBox="0 0 24 24"
    >
      <path strokeLinecap="round" strokeLinejoin="round" d="M19 9l-7 7-7-7" />
    </svg>
  </div>
</div>


<p className="text-[13px] font-normal text-[#3324E3] underline text-right">Add Branch Location</p>
            </div>
          )}
        </div>

        {/* Payments & Billing */}
        <div className="rounded-sm overflow-hidden">
          <button
            onClick={() => toggleStep(3)}
            className="w-full flex justify-between items-center px-4 py-3 bg-tables hover:bg-gray-200 transition"
          >
            <h2 className="font-medium text-[14px] text-[#050013]">Payments & Billing</h2>
            <FaChevronDown className={`transition-transform duration-200 ${openStep === 3 ? "rotate-180" : "rotate-0"}`} />
          </button>
          {openStep === 3 && (
            <div className="p-4 bg-white space-y-4">
                 <div className="relative w-full">
  <select
    defaultValue=""
    className="w-full appearance-none border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-[#3324E3] text-[#76787A]"
    onChange={(e) => {
      e.target.classList.remove("text-[#76787A]");
      e.target.classList.add("text-[#050013]");
    }}
  >
    <option value="" disabled>
    Billing Cycle
    </option>
    <option value="private">Private Limited</option>
    <option value="public">Public Limited</option>
    <option value="llp">LLP</option>
    <option value="prop">Proprietorship</option>
  </select>

  <div className="pointer-events-none absolute inset-y-0 right-3 flex items-center text-gray-500">
    <svg
      className="h-4 w-4"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      viewBox="0 0 24 24"
    >
      <path strokeLinecap="round" strokeLinejoin="round" d="M19 9l-7 7-7-7" />
    </svg>
  </div>
</div>
                <input
    type="url"
    placeholder="Invoice Email for Billing"
    className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-[#3324E3]"
  />
  <div className="mt-1">
  <p className="text-[14px] font-medium text-[#76787A] pb-3 dark:text-white">
  Contract Upload
  </p>

  <div className="shadow-none p-4 mt-1 bg-white border border-gray-200 rounded-lg dark:bg-gray-800 dark:border-gray-700">
    <div className="flex items-center justify-center w-full">
      <label className="flex flex-col items-center justify-center w-full border-2 border-gray-300 border-dashed rounded-lg cursor-pointer py-8">
        <div className="flex items-center justify-center pt-5 pb-6">
          <svg xmlns="http://www.w3.org/2000/svg" width="44" height="32" fill="none">
            <path
              fill="#76787A"
              d="M21.818 0c3.348 0 6.705 1.265 9.258 3.818 1.981 1.981 3.161 4.46 3.606 7.03 5.07.824 8.954 5.192 8.954 10.485C43.636 27.213 38.85 32 32.97 32H9.212A9.213 9.213 0 0 1 0 22.788c0-4.952 3.918-8.956 8.818-9.167-.142-3.528 1.07-7.115 3.758-9.803A13.02 13.02 0 0 1 21.818 0m0 1.94a11.08 11.08 0 0 0-7.879 3.257 11.13 11.13 0 0 0-3.181 9.227.97.97 0 0 1-.97 1.091h-.576a7.236 7.236 0 0 0-7.273 7.273 7.236 7.236 0 0 0 7.273 7.273H32.97a8.713 8.713 0 0 0 8.727-8.728 8.72 8.72 0 0 0-7.924-8.697.97.97 0 0 1-.879-.848c-.28-2.41-1.33-4.74-3.182-6.591a11.12 11.12 0 0 0-7.894-3.258m0 9.696c.26.005.513.116.652.243l5.333 4.848c.399.347.418 1.001.076 1.38-.342.377-1.003.403-1.379.06l-3.712-3.38v10.425a.97.97 0 1 1-1.94 0V14.788l-3.712 3.379c-.376.343-1.018.3-1.378-.06-.374-.375-.304-1.04.075-1.38l5.334-4.848c.213-.195.394-.243.651-.243"
            />
          </svg>
          <p className="text-sm text-dark-grey dark:text-gray-400">
            <span className="px-3 text-[14px]">Click or drag file to this area to upload</span>
          </p>
        </div>
        <input id="dropzone-file" className="hidden" type="file" name="driverLicenceFile" />
      </label>
    </div>
  </div>

  <p className="text-[#76787A] text-[13px] py-3">Formats accepted are PNG & JPG</p>
  <p className="text-[#050013] text-[13px] mt-2">If you do not have a file you can see the sample below</p>

  <div className="bg-tables p-3 flex justify-between items-center w-full mt-3" style={{ borderRadius: "10px" }}>
    <div>
      <p className="text-dark text-sm font-semibold mb-1">Sample Certificate</p>
      <p className="text-gray-400 text-sm">PNG 1.2MB</p>
    </div>
    <div className="flex items-center gap-2 text-blue-800 font-semibold">
      <svg
        stroke="currentColor"
        fill="currentColor"
        strokeWidth="0"
        viewBox="0 0 24 24"
        className="w-[20px] h-[20px]"
        height="1em"
        width="1em"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          fill="none"
          strokeWidth="2"
          d="M2.99787498,0.999999992 L17.4999998,0.999999992 L20.9999998,4.50000005 L21,23 L3,23 L2.99787498,0.999999992 Z M16,1 L16,6 L21,6 M12,9 L12,18 M8,15 L12,19 L16,15"
        />
      </svg>
      <p className="text-sm">Download</p>
    </div>
  </div>

  <div className="flex justify-end items-center gap-2 py-6">
    <button
      type="button"
      aria-label="Cancel"
      className="py-2.5 px-10 text-sm font-medium text-gray-900 focus:outline-none bg-white rounded-lg border border-gray-200 hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-4 focus:ring-gray-100 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700"
      style={{ borderRadius: "50px" }}
    >
      Cancel
    </button>
    <button
      type="button"
      aria-label="Upload"
      className="text-white bg-gray-800 hover:bg-gray-900 focus:outline-none focus:ring-4 focus:ring-gray-300 font-medium rounded-lg text-sm px-5 py-2.5 flex items-center gap-2 dark:bg-gray-800 dark:hover:bg-gray-700 dark:focus:ring-gray-700 dark:border-gray-700"
      style={{ borderRadius: "50px" }}
    >
      <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" className="size-4">
        <path strokeLinecap="round" strokeLinejoin="round" d="M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5m-13.5-9L12 3m0 0 4.5 4.5M12 3v13.5" />
      </svg>
      <span>Upload</span>
    </button>
  </div>
</div>
<input
    type="url"
    placeholder="Legal Representative name"
    className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-[#3324E3]"
  />
    <div className="mt-1">
  <p className="text-[14px] font-medium text-[#76787A] pb-3 dark:text-white">
  Upload Legal Representative Signature
  </p>

  <div className="shadow-none p-4 mt-1 bg-white border border-gray-200 rounded-lg dark:bg-gray-800 dark:border-gray-700">
    <div className="flex items-center justify-center w-full">
      <label className="flex flex-col items-center justify-center w-full border-2 border-gray-300 border-dashed rounded-lg cursor-pointer py-8">
        <div className="flex items-center justify-center pt-5 pb-6">
          <svg xmlns="http://www.w3.org/2000/svg" width="44" height="32" fill="none">
            <path
              fill="#76787A"
              d="M21.818 0c3.348 0 6.705 1.265 9.258 3.818 1.981 1.981 3.161 4.46 3.606 7.03 5.07.824 8.954 5.192 8.954 10.485C43.636 27.213 38.85 32 32.97 32H9.212A9.213 9.213 0 0 1 0 22.788c0-4.952 3.918-8.956 8.818-9.167-.142-3.528 1.07-7.115 3.758-9.803A13.02 13.02 0 0 1 21.818 0m0 1.94a11.08 11.08 0 0 0-7.879 3.257 11.13 11.13 0 0 0-3.181 9.227.97.97 0 0 1-.97 1.091h-.576a7.236 7.236 0 0 0-7.273 7.273 7.236 7.236 0 0 0 7.273 7.273H32.97a8.713 8.713 0 0 0 8.727-8.728 8.72 8.72 0 0 0-7.924-8.697.97.97 0 0 1-.879-.848c-.28-2.41-1.33-4.74-3.182-6.591a11.12 11.12 0 0 0-7.894-3.258m0 9.696c.26.005.513.116.652.243l5.333 4.848c.399.347.418 1.001.076 1.38-.342.377-1.003.403-1.379.06l-3.712-3.38v10.425a.97.97 0 1 1-1.94 0V14.788l-3.712 3.379c-.376.343-1.018.3-1.378-.06-.374-.375-.304-1.04.075-1.38l5.334-4.848c.213-.195.394-.243.651-.243"
            />
          </svg>
          <p className="text-sm text-dark-grey dark:text-gray-400">
            <span className="px-3 text-[14px]">Click or drag file to this area to upload</span>
          </p>
        </div>
        <input id="dropzone-file" className="hidden" type="file" name="driverLicenceFile" />
      </label>
    </div>
  </div>

  <p className="text-[#76787A] text-[13px] py-3">Formats accepted are PNG & JPG</p>
</div>
            </div>
          )}
        </div>

        {/* Service Details */}
        <div className="rounded-sm overflow-hidden">
          <button
            onClick={() => toggleStep(4)}
            className="w-full flex justify-between items-center px-4 py-3 bg-tables hover:bg-gray-200 transition"
          >
            <h2 className="font-medium text-[14px] text-[#050013]">Service Details</h2>
            <FaChevronDown className={`transition-transform duration-200 ${openStep === 4 ? "rotate-180" : "rotate-0"}`} />
          </button>
          {openStep === 4 && (
            <div className="p-4 bg-white space-y-4">
                               <div className="relative w-full">
  <select
    defaultValue=""
    className="w-full appearance-none border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-[#3324E3] text-[#76787A]"
    onChange={(e) => {
      e.target.classList.remove("text-[#76787A]");
      e.target.classList.add("text-[#050013]");
    }}
  >
    <option value="" disabled>
    Types of Services
    </option>
    <option value="private">Private Limited</option>
    <option value="public">Public Limited</option>
    <option value="llp">LLP</option>
    <option value="prop">Proprietorship</option>
  </select>

  <div className="pointer-events-none absolute inset-y-0 right-3 flex items-center text-gray-500">
    <svg
      className="h-4 w-4"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      viewBox="0 0 24 24"
    >
      <path strokeLinecap="round" strokeLinejoin="round" d="M19 9l-7 7-7-7" />
    </svg>
  </div>
</div>

<input
    type="text"
    placeholder="Operating Cities/Regions"
    className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-[#3324E3]"
  />

<input
    type="text"
    placeholder="Fleet Size(Total number of vehicles)"
    className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-[#3324E3]"
  />

<div className="relative w-full">
  <select
    defaultValue=""
    className="w-full appearance-none border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-[#3324E3] text-[#76787A]"
    onChange={(e) => {
      e.target.classList.remove("text-[#76787A]");
      e.target.classList.add("text-[#050013]");
    }}
  >
    <option value="" disabled>
    Available Vehicle Types
    </option>
    <option value="private">Private Limited</option>
    <option value="public">Public Limited</option>
    <option value="llp">LLP</option>
    <option value="prop">Proprietorship</option>
  </select>

  <div className="pointer-events-none absolute inset-y-0 right-3 flex items-center text-gray-500">
    <svg
      className="h-4 w-4"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      viewBox="0 0 24 24"
    >
      <path strokeLinecap="round" strokeLinejoin="round" d="M19 9l-7 7-7-7" />
    </svg>
  </div>
</div>
<p className="text-[#76787A] text-[13px]">Special Services</p>
<div className="flex gap-4 w-full">
  {/* Input 1 with image */}
  <div className="relative w-1/2">
    <img
      src="/images/chair.svg" // replace with your image path
      alt="user icon"
      className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5"
    />
    <input
      type="text"
      placeholder="First Name"
      className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-[#3324E3]"
    />
  </div>

  {/* Input 2 with image */}
  <div className="relative w-1/2">
    <img
      src="/images/seat-belt.svg" // replace with your image path
      alt="email icon"
      className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5"
    />
    <input
      type="email"
      placeholder="Email Address"
      className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-[#3324E3]"
    />
  </div>
</div>
            </div>
          )}
        </div>

        {/* Licensing & Compliance */}
        <div className="rounded-sm overflow-hidden">
          <button
            onClick={() => toggleStep(5)}
            className="w-full flex justify-between items-center px-4 py-3 bg-tables hover:bg-gray-200 transition"
          >
            <h2 className="font-medium text-[14px] text-[#050013]">Licensing & Compliance</h2>
            <FaChevronDown className={`transition-transform duration-200 ${openStep === 5 ? "rotate-180" : "rotate-0"}`} />
          </button>
          {openStep === 5 && (
            <div className="p-4 bg-white space-y-4">








<div className="mt-1">
  <p className="text-[14px] font-medium text-[#76787A] pb-3 dark:text-white">
  Business License
  </p>

  <div className="shadow-none p-4 mt-1 bg-white border border-gray-200 rounded-lg dark:bg-gray-800 dark:border-gray-700">
    <div className="flex items-center justify-center w-full">
      <label className="flex flex-col items-center justify-center w-full border-2 border-gray-300 border-dashed rounded-lg cursor-pointer py-8">
        <div className="flex items-center justify-center pt-5 pb-6">
          <svg xmlns="http://www.w3.org/2000/svg" width="44" height="32" fill="none">
            <path
              fill="#76787A"
              d="M21.818 0c3.348 0 6.705 1.265 9.258 3.818 1.981 1.981 3.161 4.46 3.606 7.03 5.07.824 8.954 5.192 8.954 10.485C43.636 27.213 38.85 32 32.97 32H9.212A9.213 9.213 0 0 1 0 22.788c0-4.952 3.918-8.956 8.818-9.167-.142-3.528 1.07-7.115 3.758-9.803A13.02 13.02 0 0 1 21.818 0m0 1.94a11.08 11.08 0 0 0-7.879 3.257 11.13 11.13 0 0 0-3.181 9.227.97.97 0 0 1-.97 1.091h-.576a7.236 7.236 0 0 0-7.273 7.273 7.236 7.236 0 0 0 7.273 7.273H32.97a8.713 8.713 0 0 0 8.727-8.728 8.72 8.72 0 0 0-7.924-8.697.97.97 0 0 1-.879-.848c-.28-2.41-1.33-4.74-3.182-6.591a11.12 11.12 0 0 0-7.894-3.258m0 9.696c.26.005.513.116.652.243l5.333 4.848c.399.347.418 1.001.076 1.38-.342.377-1.003.403-1.379.06l-3.712-3.38v10.425a.97.97 0 1 1-1.94 0V14.788l-3.712 3.379c-.376.343-1.018.3-1.378-.06-.374-.375-.304-1.04.075-1.38l5.334-4.848c.213-.195.394-.243.651-.243"
            />
          </svg>
          <p className="text-sm text-dark-grey dark:text-gray-400">
            <span className="px-3 text-[14px]">Click or drag file to this area to upload</span>
          </p>
        </div>
        <input id="dropzone-file" className="hidden" type="file" name="driverLicenceFile" />
      </label>
    </div>
  </div>

  <p className="text-[#76787A] text-[13px] py-3">Formats accepted are PNG & JPG</p>
  <p className="text-[#050013] text-[13px] mt-2">If you do not have a file you can see the sample below</p>

  <div className="bg-tables p-3 flex justify-between items-center w-full mt-3" style={{ borderRadius: "10px" }}>
    <div>
      <p className="text-dark text-sm font-semibold mb-1">Sample Certificate</p>
      <p className="text-gray-400 text-sm">PNG 1.2MB</p>
    </div>
    <div className="flex items-center gap-2 text-blue-800 font-semibold">
      <svg
        stroke="currentColor"
        fill="currentColor"
        strokeWidth="0"
        viewBox="0 0 24 24"
        className="w-[20px] h-[20px]"
        height="1em"
        width="1em"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          fill="none"
          strokeWidth="2"
          d="M2.99787498,0.999999992 L17.4999998,0.999999992 L20.9999998,4.50000005 L21,23 L3,23 L2.99787498,0.999999992 Z M16,1 L16,6 L21,6 M12,9 L12,18 M8,15 L12,19 L16,15"
        />
      </svg>
      <p className="text-sm">Download</p>
    </div>
  </div>

  <div className="flex justify-end items-center gap-2 pt-6">
    <button
      type="button"
      aria-label="Cancel"
      className="py-2.5 px-10 text-sm font-medium text-gray-900 focus:outline-none bg-white rounded-lg border border-gray-200 hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-4 focus:ring-gray-100 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700"
      style={{ borderRadius: "50px" }}
    >
      Cancel
    </button>
    <button
      type="button"
      aria-label="Upload"
      className="text-white bg-gray-800 hover:bg-gray-900 focus:outline-none focus:ring-4 focus:ring-gray-300 font-medium rounded-lg text-sm px-5 py-2.5 flex items-center gap-2 dark:bg-gray-800 dark:hover:bg-gray-700 dark:focus:ring-gray-700 dark:border-gray-700"
      style={{ borderRadius: "50px" }}
    >
      <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" className="size-4">
        <path strokeLinecap="round" strokeLinejoin="round" d="M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5m-13.5-9L12 3m0 0 4.5 4.5M12 3v13.5" />
      </svg>
      <span>Upload</span>
    </button>
  </div>
</div>


<p className="text-[14px] font-medium text-[#76787A] pb-3 dark:text-white">Insurance Details</p>
<input
      type="email"
      placeholder="Company"
      className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-[#3324E3]"
    />
        <input
      type="email"
      placeholder="Policy Number"
      className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-[#3324E3]"
    />
        <input
      type="email"
      placeholder="Expiry Date"
      className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-[#3324E3]"
    />

<div className="mt-1">
  <p className="text-[14px] font-medium text-[#76787A] pb-3 dark:text-white">
  Regulatory Certificates (if applicable, e.g., transport authority approvals)
  </p>

  <div className="shadow-none p-4 mt-1 bg-white border border-gray-200 rounded-lg dark:bg-gray-800 dark:border-gray-700">
    <div className="flex items-center justify-center w-full">
      <label className="flex flex-col items-center justify-center w-full border-2 border-gray-300 border-dashed rounded-lg cursor-pointer py-8">
        <div className="flex items-center justify-center pt-5 pb-6">
          <svg xmlns="http://www.w3.org/2000/svg" width="44" height="32" fill="none">
            <path
              fill="#76787A"
              d="M21.818 0c3.348 0 6.705 1.265 9.258 3.818 1.981 1.981 3.161 4.46 3.606 7.03 5.07.824 8.954 5.192 8.954 10.485C43.636 27.213 38.85 32 32.97 32H9.212A9.213 9.213 0 0 1 0 22.788c0-4.952 3.918-8.956 8.818-9.167-.142-3.528 1.07-7.115 3.758-9.803A13.02 13.02 0 0 1 21.818 0m0 1.94a11.08 11.08 0 0 0-7.879 3.257 11.13 11.13 0 0 0-3.181 9.227.97.97 0 0 1-.97 1.091h-.576a7.236 7.236 0 0 0-7.273 7.273 7.236 7.236 0 0 0 7.273 7.273H32.97a8.713 8.713 0 0 0 8.727-8.728 8.72 8.72 0 0 0-7.924-8.697.97.97 0 0 1-.879-.848c-.28-2.41-1.33-4.74-3.182-6.591a11.12 11.12 0 0 0-7.894-3.258m0 9.696c.26.005.513.116.652.243l5.333 4.848c.399.347.418 1.001.076 1.38-.342.377-1.003.403-1.379.06l-3.712-3.38v10.425a.97.97 0 1 1-1.94 0V14.788l-3.712 3.379c-.376.343-1.018.3-1.378-.06-.374-.375-.304-1.04.075-1.38l5.334-4.848c.213-.195.394-.243.651-.243"
            />
          </svg>
          <p className="text-sm text-dark-grey dark:text-gray-400">
            <span className="px-3 text-[14px]">Click or drag file to this area to upload</span>
          </p>
        </div>
        <input id="dropzone-file" className="hidden" type="file" name="driverLicenceFile" />
      </label>
    </div>
  </div>

  <p className="text-[#76787A] text-[13px] py-3">Formats accepted are PNG & JPG</p>
  <p className="text-[#050013] text-[13px] mt-2">If you do not have a file you can see the sample below</p>

  <div className="bg-tables p-3 flex justify-between items-center w-full mt-3" style={{ borderRadius: "10px" }}>
    <div>
      <p className="text-dark text-sm font-semibold mb-1">Sample Certificate</p>
      <p className="text-gray-400 text-sm">PNG 1.2MB</p>
    </div>
    <div className="flex items-center gap-2 text-blue-800 font-semibold">
      <svg
        stroke="currentColor"
        fill="currentColor"
        strokeWidth="0"
        viewBox="0 0 24 24"
        className="w-[20px] h-[20px]"
        height="1em"
        width="1em"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          fill="none"
          strokeWidth="2"
          d="M2.99787498,0.999999992 L17.4999998,0.999999992 L20.9999998,4.50000005 L21,23 L3,23 L2.99787498,0.999999992 Z M16,1 L16,6 L21,6 M12,9 L12,18 M8,15 L12,19 L16,15"
        />
      </svg>
      <p className="text-sm">Download</p>
    </div>
  </div>

  <div className="flex justify-end items-center gap-2 py-6">
    <button
      type="button"
      aria-label="Cancel"
      className="py-2.5 px-10 text-sm font-medium text-gray-900 focus:outline-none bg-white rounded-lg border border-gray-200 hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-4 focus:ring-gray-100 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700"
      style={{ borderRadius: "50px" }}
    >
      Cancel
    </button>
    <button
      type="button"
      aria-label="Upload"
      className="text-white bg-gray-800 hover:bg-gray-900 focus:outline-none focus:ring-4 focus:ring-gray-300 font-medium rounded-lg text-sm px-5 py-2.5 flex items-center gap-2 dark:bg-gray-800 dark:hover:bg-gray-700 dark:focus:ring-gray-700 dark:border-gray-700"
      style={{ borderRadius: "50px" }}
    >
      <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" className="size-4">
        <path strokeLinecap="round" strokeLinejoin="round" d="M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5m-13.5-9L12 3m0 0 4.5 4.5M12 3v13.5" />
      </svg>
      <span>Upload</span>
    </button>
  </div>
</div>

<input
      type="email"
      placeholder="Expiry Date"
      className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-[#3324E3]"
    />
            </div>
          )}
        </div>
      </div>

<div className="mt-8 text-center sm:text-right">
      <Link href="/super-admin/give-permission">
      <button className="bg-[#3324E3] text-white font-medium px-6 py-2 rounded-full hover:bg-[#291ed3] transition">
         Continue
         </button>
            </Link>
            </div>
    </div>
  );
}