import Input from "@/components/form/input/InputField";
import Label from "@/components/form/Label";
import Button from "@/components/ui/button/Button";
import { EyeCloseIcon, EyeIcon } from "@/icons";
import { LoginFormData, loginSchema } from "@/utils/schemas";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";


export default function SuperAdmin(){
     const [showPassword, setShowPassword] = useState(false);

      const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
  } = useForm<LoginFormData>({
    resolver: zodResolver(loginSchema),
  });


    const onSubmit = async (data: LoginFormData) => {
    console.log("Login Data:", data);
    // Your login logic here
  };
    return( <div className="flex flex-col flex-1 lg:w-1/2 w-full" style={{ backgroundColor: "#fff" }}>
            <div className="flex flex-col justify-center flex-1 w-full max-w-md mx-auto">
                <div>
                    <div className="mb-5 sm:mb-8">
                        <h1 className="mb-2 font-semibold text-gray-800 text-title-sm sm:text-title-md"
                            style={{ fontSize: "1.875rem", color: "#262626", fontFamily: "Poppins, sans-serif" }}>
                            Hey,<br /> Welcome Back!
                        </h1>
                        <p className="text-sm text-gray-500"
                            style={{ fontSize: "14px", color: "#8c8c8c", fontFamily: "Poppins, sans-serif" }}>
                            We are very happy to see you back!
                        </p>
                    </div>
                    <form  onSubmit={handleSubmit(onSubmit)}>
                        <div className="space-y-6">
                            <div>
                                <Label className="font-semibold">
                                    <span style={{ fontSize: "14px", color: "#595959" }}>Email</span>
                                </Label>
                                <Input
                                    placeholder="Enter email"
                                    type="text"
                                    style={{
                                        backgroundColor: "white",
                                        color: "black",
                                        padding: "10.5px 14px",
                                        borderRadius: "5px",
                                        borderColor: "#e1e1e1",
                                    }}
                                />
                            </div>
                            <div>
                                <Label className="font-semibold">
                                    <span style={{ fontSize: "14px", color: "#595959" }}>Password</span>
                                    {/* <span className="text-error-500">*</span> */}
                                </Label>
                                <div className="relative">
                                    <Input
                                        placeholder="Enter password"
                                        type={showPassword ? "text" : "password"}
                                        onChange={(e) => setPassword(e.target.value)}
                                        style={{
                                            backgroundColor: "white",
                                            color: "black",
                                            padding: "10.5px 14px",
                                            borderRadius: "5px",
                                            borderColor: "#e1e1e1",
                                        }}
                                    />
                                    <span
                                        onClick={() => setShowPassword(!showPassword)}
                                        className="absolute z-30 -translate-y-1/2 cursor-pointer right-4 top-1/2"
                                    >
                                        {showPassword ? <EyeIcon className="fill-gray-500" /> :
                                            <EyeCloseIcon className="fill-gray-500" />}
                                    </span>

                                </div>
                                {error && <p className="text-red-500 text-sm pt-2">{error}</p>}

                                <p className="text-sm text-[#76787A] mt-2 text-right cursor-pointer"
                                    style={{ fontSize: "14px", color: "#8c8c8c", fontFamily: "Poppins, sans-serif" }}>
                                    Forgot Password
                                </p>
                            </div>

                            <div>
                                <Button className="w-full" size="md" disabled={loading}>
                                    {loading ? "Logging in..." : "Login"}
                                </Button>
                            </div>

                            <p className="text-center text-sm mt-3 text-gray-500 font-normal">
                                Don’t have an account?{" "}
                                <span className="text-blue-700 font-semibold cursor-pointer hover:underline">
                                    Sign up here
                                </span>
                            </p>


                        </div>
                    </form>
                </div>
            </div >
        </div >  )
}