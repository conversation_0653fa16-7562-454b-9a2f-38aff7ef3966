"use client";

import React from "react";
import Link from "next/link";
import { FiEdit } from "react-icons/fi";
import { GoDownload } from "react-icons/go";


export default function Setting() {
  return (
    <div className="rounded-lg shadow-sm border border-gray-200 ">
      {/* Top Row - All in one line */}
      <div className="rounded-t-lg  bg-white p-6 flex flex-col lg:flex-row lg:items-start justify-between gap-6 border-b">
  
  {/* Left: Logo + Company Info */}
  <div className="flex items-center gap-4 min-w-[250px]">
    <div className="w-18 h-18 rounded-full bg-gray-100 flex items-center justify-center">
      <img src="/images/name-logo.svg" alt="Icon" className="w-18 h-18 object-contain" />
    </div>
    <div>
      <h2 className="text-[24px] text-[#050013] font-normal">Silverline Solutions</h2>
      <div className="flex items-center gap-2 text-[11px] text-[#13BB76] font-medium">
        <span className="w-2 h-2 bg-[#13BB76] rounded-full"></span>
        Active
      </div>
    </div>
  </div>

  {/* Middle: Contact Details */}
  <div className="flex flex-col sm:flex-row sm:items-start sm:justify-center sm:gap-12 text-sm flex-1 border-l">
    <div className="space-y-3">
      <p className="font-medium text-[#050013] text-[14px]">
        <span className="font-normal text-[#76787A] text-[13px]">Email Id : </span> <EMAIL>
      </p>
      <p className="font-medium text-[#050013] text-[14px]">
        <span className="font-normal text-[#76787A] text-[13px]">Phone no : </span> *************
      </p>
      <p className="font-medium text-[#050013] text-[14px]">
        <span className="font-normal text-[#76787A] text-[13px]">Subscription Plan : </span> Basic
      </p>
    </div>
    <div className="space-y-3">
      <p className="font-medium text-[#050013] text-[14px]">
        <span className="font-normal text-[#76787A] text-[13px]">Primary Contact Name</span> Oliver Thompson
      </p>
      <p className="font-medium text-[#050013] text-[14px]">
        <span className="font-normal text-[#76787A] text-[13px]">Tax Identification Number (TIN/VAT): </span> 45485
      </p>
      <p className="font-medium text-[#050013] text-[14px]">
        <span className="font-normal text-[#76787A] text-[13px]">Address : </span> 12 Street, The Hague, Netherlands
      </p>
      <p className="text-right">
      <Link href="/about" className="text-[#3324E3] text-[13px] underline">
      View More
      </Link>
      </p>
    </div>
  </div>

  {/* Right: Action Buttons */}
  <div className="flex items-center gap-1 justify-end">
    <button className="p-2 rounded-md hover:bg-gray-100 text-[#76787A]" title="Edit"><FiEdit size={18}/>
    </button>
    <button className="p-2 rounded-md hover:bg-gray-100 text-[#76787A]" title="Download"><GoDownload size={20}/>
    </button>
  </div>
</div>


      {/* Overview Section */}
      <div className="rounded-b-lg p-6 bg-[#F6F8FB]">
        <h3 className="text-[13px] text-[#76787A] font-normal mb-4">Overview</h3>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
          <Card subheading="Finances" value="250/500" subtext="Rides Used" link="View Invoices" icon={<img src="/images/finance.svg" alt="Icon"
          className="w-15 h-15 object-contain"/>}/>
          <Card subheading="Members" icon={<img src="/images/members.svg" alt="Icon"
          className="w-15 h-15 object-contain"/>} bg="bg-yellow-50" value="02" link="View All" />
          <Card icon={<img src="/images/vehicle.svg" alt="Icon"
          className="w-15 h-15 object-contain"/>} bg="bg-blue-50" value="17" subtext="Vehicles" link="View All"/>
          <Card icon={<img src="/images/driver.svg" alt="Icon"
          className="w-15 h-15 object-contain"/>} bg="bg-yellow-50" value="17" link="View All" subtext="Drivers"/>
          <Card icon={<img src="/images/client.svg" alt="Icon"
          className="w-15 h-15 object-contain"/>} subtext="Clients" bg="bg-red-50" value="30" link="View All" />
          <Card icon={<img src="/images/passanger.png" alt="Icon"
          className="w-15 h-15 object-contain"/>} subtext="Passenger" bg="bg-purple-50" value="750" link="View All" />
        </div>
      </div>
    </div>
  );
}

// Card Component
function Card({ icon, bg, value, subtext, link ,subheading}) {
  return (
    <div className="bg-white rounded-lg border border-gray-200 flex flex-col justify-between">
      <div className="flex items-center justify-between  px-6 py-3">
        <div className="space-y-1">
        {subheading && <p className="text-[13px] text-[#76787A] font-normal">{subheading}</p>}
          <p className="text-[24px] text-[#050013] font-semibold">{value}</p>
          {subtext && <p className="text-[10px] text-[#76787A]">{subtext}</p>}
        </div>
        <div
          className={`w-15 h-15 rounded-full ${bg} flex items-center justify-center text-lg`}
        >
          {icon}
        </div>
      </div>
      <div className="bg-[#F6F8FB] px-6 py-4 rounded-b-lg">
      <button className="text-[#3324E3] text-[13px] underline text-left">
        {link}
      </button>
      </div>
    </div>
  );
}
