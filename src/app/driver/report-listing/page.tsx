"use client";
import React, { useState } from "react";
import {
  FiVolume2,
  FiAlertTriangle,
  FiFileText,
  FiMapPin,
  FiCreditCard,
  FiX,
} from "react-icons/fi";

const reports = [
  {
    id: 1,
    type: "Safety Concern",
    description: "Passenger misbehaved and violated seatbelt rules.",
    audioUrl: "/audio/sample1.mp3",
    imageUrl: "/images/seatbelt.jpg",
  },
  {
    id: 2,
    type: "Fare Dispute",
    description: "Charged more than estimated fare.",
    audioUrl: "/audio/sample2.mp3",
    imageUrl: "/images/open-door.jpg",
  },
  {
    id: 3,
    type: "Navigation Issue",
    description: "Driver was guided to incorrect pickup point.",
    audioUrl: "/audio/sample3.mp3",
    imageUrl: "/images/pickup.jpg",
  },
  {
    id: 4,
    type: "Payment Issue",
    description: "UPI payment failed after ride completion.",
    audioUrl: "/audio/sample4.mp3",
    imageUrl: "/images/pickup.jpg",
  },
];

const getIcon = (type) => {
  switch (type) {
    case "Safety Concern":
      return <FiAlertTriangle className="text-red-500 text-xl" />;
    case "Fare Dispute":
      return <FiFileText className="text-yellow-500 text-xl" />;
    case "Navigation Issue":
      return <FiMapPin className="text-pink-500 text-xl" />;
    case "Payment Issue":
      return <FiCreditCard className="text-purple-500 text-xl" />;
    default:
      return <FiFileText className="text-gray-400 text-xl" />;
  }
};

const ReportListingPage = () => {
  const [modalImage, setModalImage] = useState(null);

  return (
    <div className="max-w-6xl mx-auto px-4">
      <h1 className="text-[18px] font-bold text-gray-800 mb-10">Reported Issues</h1>

      <div className="space-y-6">
        {reports.map((report) => (
          <div
            key={report.id}
            className="flex flex-col sm:flex-row sm:items-center justify-between gap-4 bg-white border border-gray-100 rounded-2xl shadow-md hover:shadow-lg transition p-6"
          >
            {/* Left: Icon + Info */}
            <div className="flex items-start gap-4 flex-1">
              <div className="p-3 bg-gray-100 rounded-full shadow-sm">
                {getIcon(report.type)}
              </div>

              <div className="space-y-2">
                <p className="text-gray-800 text-[15px] leading-relaxed">
                  {report.description}
                </p>

                <button
                  onClick={() => new Audio(report.audioUrl).play()}
                  className="inline-flex items-center gap-1 text-blue-600 text-sm font-medium px-3 py-1.5 bg-blue-50 hover:bg-blue-100 rounded-full transition"
                >
                  <FiVolume2 className="text-base" />
                  Play Audio
                </button>
              </div>
            </div>

            {/* Right: Image Preview */}
            <div
              onClick={() => setModalImage(report.imageUrl)}
              className="w-full sm:w-40 h-28 rounded-xl overflow-hidden border hover:scale-[1.03] transition cursor-pointer"
            >
              <img
                src={report.imageUrl}
                alt="Issue"
                className="w-full h-full object-cover"
                onError={(e) =>
                  (e.target.src = "https://via.placeholder.com/300x200?text=No+Image")
                }
              />
            </div>
          </div>
        ))}
      </div>

      {/* Modal */}
      {modalImage && (
        <div
          className="fixed inset-0 bg-black/70 z-50 flex items-center justify-center"
          onClick={() => setModalImage(null)}
        >
          <div className="relative max-w-3xl w-full px-4">
            <button
              className="absolute top-4 right-4 text-white text-2xl"
              onClick={() => setModalImage(null)}
            >
              <FiX />
            </button>
            <img
              src={modalImage}
              alt="Full view"
              className="w-full max-h-[80vh] object-contain rounded-xl border shadow-2xl"
            />
          </div>
        </div>
      )}
    </div>
  );
};

export default ReportListingPage;
