"use client";

import { useState, useEffect } from "react";
import { useHeaderTitle } from "@/context/HeaderTitleContext";
import { FiAlertTriangle, FiMic, FiCamera, FiX } from "react-icons/fi";
import { FaEuroSign, FaUser } from "react-icons/fa";
import { HiOutlineCurrencyEuro } from "react-icons/hi";
import { BiNavigation } from "react-icons/bi";
import { IoIosArrowForward } from "react-icons/io";
import { TbReceipt, TbUsers } from "react-icons/tb";

const issues = [
  {
    label: "Safety Concern",
    icon: <FiAlertTriangle className="text-xl text-red-400" />, 
    color: "bg-red-100",
  },
  {
    label: "Fare Dispute",
    icon: <TbReceipt className="text-xl text-orange-400" />, 
    color: "bg-orange-100",
  },
  {
    label: "Payment issue",
    icon: <HiOutlineCurrencyEuro className="text-xl text-blue-500" />, 
    color: "bg-purple-100",
  },
  {
    label: "Passenger Behaviour",
    icon: <TbUsers className="text-xl text-gray-600" />, 
    color: "bg-gray-100",
  },
  {
    label: "Navigation issue",
    icon: <BiNavigation className="text-xl text-rose-400" />, 
    color: "bg-rose-100",
  },
];

export default function ReportPage() {
  const { setTitle } = useHeaderTitle();
  const [selected, setSelected] = useState(null);

  useEffect(() => {
    setTitle("Report");
  }, [setTitle]);

  return (
    <div className="bg-tables p-6 rounded-2xl border w-full relative">
      <h2 className="text-lg font-normal mb-6 text-[#050013]">Report an issue</h2>

      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
        {issues.slice(0, 3).map((item, idx) => (
          <button
            key={idx}
            onClick={() => setSelected(item)}
            className="flex items-center justify-between bg-white p-4 rounded-xl shadow-sm hover:shadow-md transition"
          >
            <div className="flex items-center gap-3">
              <div className={`w-10 h-10 ${item.color} rounded-full flex items-center justify-center`}>
                {item.icon}
              </div>
              <span className="text-sm font-medium text-left text-[#050013]">{item.label}</span>
            </div>
            <IoIosArrowForward className="text-gray-400" />
          </button>
        ))}
      </div>

      <p className="text-sm text-[#76787A] mt-6 mb-2">Other issues</p>

      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
        {issues.slice(3).map((item, idx) => (
          <button
            key={idx}
            onClick={() => setSelected(item)}
            className="flex items-center justify-between bg-white p-4 rounded-xl shadow-sm hover:shadow-md transition"
          >
            <div className="flex items-center gap-3">
              <div className={`w-10 h-10 ${item.color} rounded-full flex items-center justify-center`}>
                {item.icon}
              </div>
              <span className="text-sm font-medium text-left text-[#050013]">{item.label}</span>
            </div>
            <IoIosArrowForward className="text-gray-400" />
          </button>
        ))}
      </div>

      {/* Slide-in Modal */}
      {selected && (
        <div className="fixed inset-0 z-[999] flex justify-end bg-black/30 transition-opacity duration-300 ease-in-out">
          <div className="w-full sm:max-w-md bg-white absolute bottom-0 h-[600px] overflow-y-scroll sm:relative sm:h-full 
          sm:overflow-visible shadow-lg transform transition-all duration-500 translate-x-0 sm:translate-x-0 r rounded-tl-[18px] rounded-tr-[18px] sm:rounded-tr-none 
          flex flex-col justify-between">
            <div>
              <div className="flex justify-between items-center mb-6 p-6 bg-cstm-grey">
                <h3 className="text-lg font-normal text-[#050013]">{selected.label}</h3>
                <button
                  onClick={() => setSelected(null)}
                  className="text-gray-400 hover:text-black text-xl"
                >
                  <FiX />
                </button>
              </div>

              <div className="space-y-4 p-6">
                <textarea
                  placeholder="Describe your issue"
                  className="w-full border border-gray-200 rounded p-3 text-sm placeholder:text-gray-400 focus:outline-none focus:ring-1 focus:ring-blue-500"
                  rows={3}
                ></textarea>

                <div className="flex items-center border border-gray-200 rounded px-3 py-5 justify-between">
                  <input
                    type="text"
                    placeholder="Record issue"
                    className="flex-1 text-sm bg-transparent outline-none placeholder:text-gray-400"
                  />
                  <span className="bg-cstm-grey p-2 rounded-[20px]"> <FiMic className="text-blue-500 text-lg" /></span>
                 
                </div>
                <div className="border p-2 rounded">
                <div className="border border-dashed border-gray-300 rounded p-6 flex flex-col items-center justify-center text-gray-400">
                  <FiCamera className="text-2xl mb-2" />
                  <span className="text-sm">Add photo</span>
                </div>
                </div>
              </div>
            </div>

            <div className="p-6 flex justify-end border-t">
              <button className="py-3 px-6 w-full sm:w-auto rounded-full bg-[#3707EF] hover:bg-[#3e1ed0] text-white text-[13px] font-semibold">
                Submit Report
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
