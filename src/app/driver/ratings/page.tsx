"use client";
import { useHeaderTitle } from "@/context/HeaderTitleContext";
import { useEffect } from "react";
import { FaStar } from 'react-icons/fa';
import { RxStarFilled } from "react-icons/rx";





const feedbacks = [
    {
      name: '<PERSON><PERSON>',
      comment: 'Excellent driving skills and top-notch professionalism—truly impressive!',
      rating: 4.5,
    },
    {
      name: '<PERSON>',
      comment: 'Good conversation',
      rating: 4.5,
    },
    {
      name: '<PERSON>',
      comment: 'The ride was smooth, and the driver was very professional and courteous. Thank you for a great experience!',
      rating: 4.5,
    },
  ];
  
  const tips = [
    'Keep your vehicle clean',
    'Choose optimal routes to avoid traffic',
    'Maintain professional communication',
  ];

  

export default function FeedbackCard() {
    const { setTitle } = useHeaderTitle();
    useEffect(() => {
        setTitle("Earnings");
    }, []);
    return <div className="p-6 text-xl font-semibold"> 
    
    <div className="space-y-4 mx-auto">
      {/* Card Section */}
      <div className="bg-white rounded-2xl border flex flex-col md:flex-row overflow-hidden">
        {/* Profile Section */}
        <div className="flex flex-col items-center justify-center p-6 md:w-1/3 border-r border-gray-100">
          <img
            src="/images/avatar.jpg"
            alt="User"
            className="w-20 h-20 rounded-full object-cover mb-4"
          />
          <div className="flex items-center space-x-1 text-orange-400 text-xl">
            {[1, 2, 3, 4].map((_, i) => (
              <RxStarFilled key={i} />
            ))}
            <RxStarFilled className="text-gray-300" />
          </div>
          <p className="text-2xl text-[#050013] font-normal mt-2">4.5</p>
          <p className="text-sm text-[#76787A] font-normal mt-2">Based on 70 rides</p>
        </div>

        {/* Feedback Section */}
        <div className="bg-gray-50 flex-1 p-6">
          <h3 className="text-[#76787A] text-sm font-normal mb-4">Recent Feedback</h3>
          <div className="space-y-4">
            {feedbacks.map((item, idx) => (
              <div key={idx} className="bg-white p-4 rounded-lg shadow-sm flex justify-between items-start">
                <div>
                  <p className="text-sm font-normal text-[#050013] mb-1">"{item.comment}"</p>
                  <p className="text-xs text-[#76787A]">{item.name}</p>
                </div>
                <div className="flex items-center gap-3 text-orange-400">
                  <RxStarFilled />
                  <span className="text-sm font-semibold text-black">{item.rating}</span>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Tips Section */}
      <div className="bg-yellow-50 border rounded-2xl">
        <h3 className="yellow-bg text-yellow-600 font-normal text-sm p-4 rounded-t-[15px]">Tips for improvement</h3>
        <div className="p-4">
        <ul className="space-y-2 pl-4 list-disc text-sm font-normal text-gray-700">
          {tips.map((tip, idx) => (
            <li key={idx} className="text-[#050013] before:bg-gradient-to-r before:from-[#251AA1] before:to-[#2E20CB]">{tip}</li>
          ))}
        </ul>
        </div>
      </div>
    </div>
    
    
    
    
    </div>;
}