"use client";
import { useState, useEffect } from "react";
import { useHeaderTitle } from "@/context/HeaderTitleContext";
import { FiPhoneCall } from "react-icons/fi";
import { TbUser } from "react-icons/tb";
import { BsFillSendFill } from "react-icons/bs";
import { HiChevronLeft } from "react-icons/hi";

const chats = [
  { id: "#R12345", name: "J<PERSON>", message: "I'm waiting at the main entrance.", time: "9:16AM", active: true },
  { id: "Dispatcher", name: "D", message: "Please confirm once the pickup is completed", time: "9:17AM", active: false },
  { id: "#R12343", name: "<PERSON><PERSON>", message: "I'm waiting at the main entrance.", date: "18 Jan 2025", active: false },
  { id: "#R12342", name: "B<PERSON>", message: "Where are you", date: "16 Jan 2025", active: false },
  { id: "#R12340", name: "<PERSON><PERSON>", message: "I'm waiting at the main entrance.", date: "16 Jan 2025", active: false },
  { id: "#R12339", name: "<PERSON>", message: "I'm waiting at the main entrance.", date: "12 Jan 2025", active: false },
];

export default function ChatPage() {
  const { setTitle } = useHeaderTitle();
  const [message, setMessage] = useState("");
  const [selectedChat, setSelectedChat] = useState(null);
  const [showChatWindowOnMobile, setShowChatWindowOnMobile] = useState(false); // ⭐ for mobile toggle

  useEffect(() => {
    setTitle("Chat");
  }, [setTitle]);

  const handleChatClick = (chat) => {
    setSelectedChat(chat);
    setShowChatWindowOnMobile(true); // 👈 show chat window in mobile
  };

  const handleBackToList = () => {
    setShowChatWindowOnMobile(false); // 👈 go back to chat list in mobile
  };

  return (
    <div className="flex md:flex-row flex-col max-w-7xl mx-auto h-full sm:h-screen border rounded-2xl overflow-hidden bg-white">
      
      {/* Sidebar - Chat List */}
      <div
        className={`w-full md:w-2/3 bg-cstm-grey border-r p-4 overflow-y-auto md:block ${
          showChatWindowOnMobile ? "hidden" : "block"
        }`}
      >
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-sm font-normal">Chat</h2>
        </div>

        <h4 className="text-sm text-[#76787A] mb-2">Active Chat</h4>
        {chats.slice(0, 2).map((chat, idx) => (
          <div
            key={idx}
            onClick={() => handleChatClick(chat)}
            className={`flex items-start gap-3 p-3 rounded-xl cursor-pointer ${chat.active ? 'bg-green-100' : 'hover:bg-gray-100'}`}
          >
            <div className="relative inline-block">
              <div className="w-8 h-8 rounded-full bg-purple-100 flex items-center justify-center font-normal text-purple-600 text-sm">
                {chat.name}
              </div>
              <span className="absolute bottom-0 right-0 w-3 h-3 rounded-full bg-green-500 border-2 border-white"></span>
            </div>
            <div className="text-sm">
              <p className="font-semibold">{chat.id}</p>
              <p className="text-[#76787A] text-xs">{chat.message}</p>
            </div>
            <div className="ml-auto text-xs text-[#76787A] whitespace-nowrap">{chat.time}</div>
          </div>
        ))}

        <h4 className="text-sm text-[#76787A] mt-6 mb-2">Chat History</h4>
        {chats.slice(2).map((chat, idx) => (
          <div
            key={idx}
            onClick={() => handleChatClick(chat)}
            className="flex items-start gap-3 p-3 rounded-xl cursor-pointer hover:bg-gray-100"
          >
            <div className="w-8 h-8 rounded-full bg-orange-100 flex items-center justify-center font-normal text-sm text-orange-500">
              {chat.name}
            </div>
            <div className="text-sm">
              <p className="font-semibold text-[#050013]">{chat.id}</p>
              <p className="text-[#76787A] text-xs">{chat.message}</p>
            </div>
            <div className="ml-auto text-xs text-[#76787A] whitespace-nowrap">{chat.date}</div>
          </div>
        ))}
      </div>

      {/* Chat Window */}
      <div
        className={`w-full md:w-1/3 flex flex-col ${
          !showChatWindowOnMobile && "hidden md:flex"
        }`}
      >
        {selectedChat ? (
          <>
            {/* Header */}
            <div className="flex justify-between items-center p-4 border-b rounded-t-2xl bg-white">
              <div className="flex items-start gap-3">
                {/* Back icon only on mobile */}
                <HiChevronLeft
                  className="text-xl text-gray-500 mt-1 md:hidden cursor-pointer"
                  onClick={handleBackToList}
                />
                <div>
                  <p className="font-semibold text-[#050013] text-sm">{selectedChat.id}</p>
                  <p className="font-normal py-1 text-[#050013] text-sm">{selectedChat.name}</p>
                  <p className="text-xs text-[#76787A] flex items-center gap-1">
                    Passenger
                    <span className="w-2 h-2 bg-green-500 rounded-full inline-block ml-1"></span>
                    <span className="text-green-600">Online</span>
                  </p>
                </div>
              </div>
              <button className="w-8 h-8 rounded-full border border-blue-500 text-blue-600 flex items-center justify-center">
                <FiPhoneCall className="text-sm" />
              </button>
            </div>

            {/* Chat Body */}
            <div className="flex-1 p-4 overflow-y-auto space-y-4">
              <div className="flex justify-center mt-1 mb-6">
                <span className="bg-cstm-grey text-[#050013] py-1 px-3 rounded-full">Today</span>
              </div>

              <div className="flex items-start gap-3">
                <img src="/images/avatar.jpg" alt="" className="w-8 h-8 rounded-full" />
                <div>
                  <p className="text-sm bg-cstm-grey p-3 rounded-xl text-[#050013]">
                    Hi {selectedChat.name}! I'm on my way. ETA: 5 minutes.
                  </p>
                  <p className="text-xs text-[#76787A] mt-2">9:15 AM</p>
                </div>
              </div>

              <div className="flex items-end justify-end gap-2 mt-6">
                <div>
                  <p className="text-sm text-white px-4 py-2 rounded-xl bg-gradient-to-r from-indigo-600 to-teal-400 rounded-tr-none">
                    {selectedChat.message}
                  </p>
                  <p className="text-xs text-[#76787A] mt-2 text-right">{selectedChat.time || selectedChat.date}</p>
                </div>
                <div className="w-6 h-6 rounded-full border flex items-center justify-center bg-cstm-grey">
                  <TbUser />
                </div>
              </div>
            </div>

            {/* Input Box */}
            <div className="p-4 space-y-3">
              <div className="flex flex-wrap gap-2">
                {["I'll be there in 2 mins", 'I have arrived', "Can't find you", 'Traffic delay'].map((txt, i) => (
                  <button key={i} className="border px-4 py-2 text-sm rounded-full text-[#050013]">
                    {txt}
                  </button>
                ))}
              </div>
              <div className="flex items-center border rounded-full px-3 py-2 mt-2">
                <input
                  type="text"
                  placeholder="Type your message..."
                  className="flex-1 border-none focus:ring-0 text-sm outline-none bg-transparent"
                  value={message}
                  onChange={(e) => setMessage(e.target.value)}
                />
                <button className="text-blue-600">
                  <BsFillSendFill className="text-xl" />
                </button>
              </div>
            </div>
          </>
        ) : (
          <div className="flex-1 flex items-center justify-center text-sm text-gray-400">
            Select a chat to view messages
          </div>
        )}
      </div>
    </div>
  );
}
