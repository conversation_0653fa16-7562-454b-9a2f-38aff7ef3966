"use client";
import { useEffect, useState, useRef } from "react";
import { useHeaderTitle } from "@/context/HeaderTitleContext";
import { X } from "lucide-react";

export default function VehicleInfoPage() {
  const { setTitle } = useHeaderTitle();
  useEffect(() => {
    setTitle("Vehicle Info");
  }, []);

  const [selectedVehicle, setSelectedVehicle] = useState(null);
  const modalRef = useRef(null);

  useEffect(() => {
    const handleEsc = (e) => {
      if (e.key === "Escape") setSelectedVehicle(null);
    };
    document.addEventListener("keydown", handleEsc);
    return () => document.removeEventListener("keydown", handleEsc);
  }, []);

  const vehicles = [
    {
      id: 1,
      type: "Sedan",
      model: "Hyundai Tucson",
      fuel: "Petrol",
      number: "NL-1234-AB",
      status: "Approved",
      driver: "<PERSON><PERSON><PERSON>",
      inspectionStatus: "Active",
      inspectionId: "INSP-0001256",
      phone: "0612345678",
      date: "15-01-2024",
      image: "/images/hyundai-tucson.png",
    },
  ];

  const getStatusClasses = (status) => {
    switch (status) {
      case "Pending":
        return "text-orange-600";
      case "Approved":
        return "text-green-600";
      case "Rejected":
        return "text-red-600";
      default:
        return "text-gray-600";
    }
  };

  const getDotColor = (status) => {
    switch (status) {
      case "Pending":
        return "bg-orange-500";
      case "Approved":
        return "bg-green-600";
      case "Rejected":
        return "bg-red-600";
      default:
        return "bg-gray-600";
    }
  };

  const getInspectionColor = (status) =>
    status === "Active" ? "text-green-600" : "text-gray-400";

  return (
    <div className="p-5 min-h-screen">
      <div className="bg-white rounded-xl shadow overflow-x-auto">
        <table className="w-full text-[14px] text-left text-[#76787A]">
          <thead className="text-[14px] bg-tables text-[#76787A] border-b">
            <tr>
              <th className="px-6 py-4 font-medium">Type</th>
              <th className="px-6 py-4 font-medium">Model</th>
              <th className="px-6 py-4 font-medium">Fuel</th>
              <th className="px-6 py-4 font-medium">Number</th>
              <th className="px-6 py-4 font-medium">Status</th>
              <th className="px-6 py-4 font-medium">Driver</th>
              <th className="px-6 py-4 font-medium">Inspection</th>
              <th className="px-6 py-4 font-medium">Date</th>
            </tr>
          </thead>
          <tbody>
            {vehicles.map((v) => (
              <tr
                key={v.id}
                onClick={() => setSelectedVehicle(v)}
                className="cursor-pointer border-b transition"
              >
                <td className="px-6 py-4 text-[#050013]">{v.type}</td>
                <td className="px-6 py-4 text-[#050013]">{v.model}</td>
                <td className="px-6 py-4 text-[#050013]">{v.fuel}</td>
                <td className="px-6 py-4 text-[#050013]">{v.number}</td>
                <td className="px-6 py-4 text-[#050013] flex items-center gap-2">
                  <span className={`h-2 w-2 rounded-full ${getDotColor(v.status)}`}></span>
                  <span className={`text-xs font-medium ${getStatusClasses(v.status)}`}>{v.status}</span>
                </td>
                <td className="px-6 py-4 text-[#050013]">{v.driver}</td>
                <td className="px-6 py-4 text-[#050013]">
                  <span className={`font-normal ${getInspectionColor(v.inspectionStatus)}`}>
                    {v.inspectionStatus}
                  </span>
                </td>
                <td className="px-6 py-4 text-[#050013]">{v.date}</td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {selectedVehicle && (
  <div
    className="fixed inset-0 bg-black/50 backdrop-blur-sm z-999 flex items-center justify-center px-4"
    onClick={(e) => {
      if (e.target === e.currentTarget) setSelectedVehicle(null);
    }}
    tabIndex={-1}
    ref={modalRef}
  >
    <div className="bg-white w-full max-w-4xl rounded-2xl shadow-xl relative animate-fade-in overflow-hidden flex flex-col md:flex-row">
      {/* Image Left */}
      <div className="w-full md:w-1/2 h-52 md:h-auto overflow-hidden">
        <img
          src={selectedVehicle.image}
          alt={selectedVehicle.model}
          className="w-full h-full object-contain"
        />
      </div>

      {/* Content Right */}
      <div className="w-full md:w-1/2 p-6 relative">
        {/* Close Button */}
        <button
          onClick={() => setSelectedVehicle(null)}
          className="absolute top-[-45%] sm:top-4 right-4 text-[#76787A] hover:text-gray-700 transition"
        >
          <X size={24} />
        </button>

        {/* Title + Status */}
        <div className="mb-4">
          <h2 className="text-xl font-semibold text-gray-800">
            {selectedVehicle.model}
          </h2>
          <p className="text-sm text-gray-500">{selectedVehicle.number}</p>
          <div className="w-12 h-[2px] bg-gray-300 my-2"></div>
          <div className="flex items-center gap-2 mb-2">
            <span className={`h-2 w-2 rounded-full ${getDotColor(selectedVehicle.status)}`}></span>
            <span className={`text-xs font-medium ${getStatusClasses(selectedVehicle.status)}`}>{selectedVehicle.status}</span>
          </div>
        </div>

        {/* Info Table */}
        <table className="w-full text-sm text-left text-gray-700 border-t border-gray-100">
          <tbody>
            <tr>
              <td className="py-2 pr-2 text-[#76787A]">Vehicle Type</td>
              <td className="py-2 font-medium text-[#050013]">{selectedVehicle.type}</td>
            </tr>
            <tr>
              <td className="py-2 pr-2 text-[#76787A]">Fuel Type</td>
              <td className="py-2 font-medium text-[#050013]">{selectedVehicle.fuel}</td>
            </tr>
            <tr>
              <td className="py-2 pr-2 text-[#76787A]">Driver Name</td>
              <td className="py-2 font-medium text-[#050013]">{selectedVehicle.driver}</td>
            </tr>
            <tr>
              <td className="py-2 pr-2 text-[#76787A]">Phone Number</td>
              <td className="py-2 font-medium text-[#050013]">{selectedVehicle.phone}</td>
            </tr>
            <tr>
              <td className="py-2 pr-2 text-[#76787A]">Inspection Status</td>
              <td className={`py-2 font-medium text-[#050013] ${getInspectionColor(selectedVehicle.inspectionStatus)}`}>{selectedVehicle.inspectionStatus}</td>
            </tr>
            <tr>
              <td className="py-2 pr-2 text-[#76787A]">Inspection ID</td>
              <td className="py-2 font-medium text-[#050013]">{selectedVehicle.inspectionId}</td>
            </tr>
            <tr>
              <td className="py-2 pr-2 text-[#76787A]">Registration Date</td>
              <td className="py-2 font-medium text-[#050013]">{selectedVehicle.date}</td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
)}

    </div>
  );
}
