"use client";

import { useEffect, useState } from "react";
import { useHeaderTitle } from "@/context/HeaderTitleContext";
import { LuPencil } from "react-icons/lu";
import { BsCheckCircleFill } from "react-icons/bs";
import { Cloud } from '@/icons';
import { FaFileAlt, FaFilePdf, FaFileWord } from "react-icons/fa";
import { BiError } from "react-icons/bi";
import { RiVipDiamondLine } from "react-icons/ri";
import { FiX } from "react-icons/fi";
import { HiOutlineCreditCard } from "react-icons/hi";
import { GoChecklist, GoCheckCircleFill } from "react-icons/go";



export default function DriverProfilePage() {
  const { setTitle } = useHeaderTitle();
  const [isRightPanel, setIsRightPanel] = useState<boolean>(false);

  useEffect(() => {
    setTitle("Driver Profile");
  }, []);

  return (
    <div className="max-w-7xl mx-auto p-4">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Left: Profile Card */}
        <div className="bg-white rounded-xl border relative">
          <div onClick={() => setIsRightPanel(true)} className="flex justify-between items-center mb-4 px-6 py-3 bg-tables rounded-xl rounded-b-none">
            <h2 className="font-semibold text-gray-900">My Profile</h2>
            <LuPencil className="text-gray-500 cursor-pointer" />
          </div>

          {/* Profile Info */}
          <div className="flex items-center gap-4 px-6 py-3">
            <img
              src="/images/avatar.jpg"
              alt="Rivka Frank"
              className="w-16 h-16 rounded-full object-cover"
            />
            <div>
              <p className="text-[18px] font-medium text-[#050013]">
                Rivka Frank
              </p>
              <p className="text-[14px] text-[#76787A] font-medium">ID: <span className="text-purple">DRV-20250123</span></p>
              <p className="text-[14px] text-[#13BB76] mt-1">● Active</p>
            </div>
          </div>

          {/* Warning */}
          <div className="p-6">            
          <div className="bg-red-50 text-[#FF2626] text-[12px] font-normal p-3 rounded-lg border border-red-100 flex items-center justify-center gap-3">
            <BiError size={20}/> Your driver’s license expires in 30 days
          </div>
          </div>
        </div>

        {/* Right: Document Section */}
        <div>
          <h2 className="font-normal text-[#76787A] text-[13px] mb-4">Document</h2>
          <div className="space-y-4">
            {/* Document 1 */}
            <div className="flex justify-between items-center p-4 bg-white rounded-xl border">
              <div className="flex items-center gap-4">
                <span className="bg-indigo-50 p-[6px] rounded-full">
                    <HiOutlineCreditCard className="text-indigo-600 text-xl" />
                    </span>
                <div>
                  <p className="font-medium text-[13px] text-[#050013]">Driver's License</p>
                  <p className="text-[13px] text-[#76787A]">Expires: Mar 15, 2025</p>
                </div>
              </div>
              <div className="flex items-center gap-1 text-[#00CA77] text-sm font-medium">
                <GoCheckCircleFill size={20} className="text-[#00CA77]" />
                Verified
              </div>
            </div>
           
            {/* Document 2 */}
            <div className="flex justify-between items-center p-4 bg-white rounded-xl border">
              <div className="flex items-center gap-4">
                <span className="bg-indigo-50 p-[6px] rounded-full"><RiVipDiamondLine className="text-indigo-600 text-xl" /></span>
                <div>
                  <p className="font-medium text-[13px] text-[#050013]">Vehicle_Insurance.pdf</p>
                  <p className="text-[13px] text-[#76787A]">Expires: Jun 15, 2027</p>
                </div>
              </div>
              <div className="flex items-center gap-1 text-[#00CA77] text-sm font-medium">
                <GoCheckCircleFill size={20} className="text-[#00CA77]" />
                Verified
              </div>
            </div>

            {/* Document 3 */}
            <div className="flex justify-between items-center p-4 bg-white rounded-xl border">
              <div className="flex items-center gap-4">
                <span className="bg-indigo-50 p-[6px] rounded-full"><GoChecklist className="text-indigo-600 text-xl" /></span>
                <div>
                  <p className="font-medium text-[13px] text-[#050013]">Background_Check.docx</p>
                  <p className="text-[13px] text-[#76787A]">Expires: April 15, 2026</p>
                </div>
              </div>
              <div className="flex items-center gap-1 text-[#00CA77] text-sm font-medium">
                <GoCheckCircleFill size={20} className="text-[#00CA77]" />
                Verified
              </div>
            </div>
          </div>
        </div>
      </div>

      {isRightPanel &&
        <div className="fixed inset-0 z-[999] flex justify-end bg-black/30 transition-opacity duration-300 ease-in-out">
  <div className="w-full sm:max-w-md bg-white absolute bottom-0 h-[600px] overflow-y-scroll sm:relative sm:h-full 
          sm:overflow-visible shadow-lg transform transition-all duration-500 translate-x-0 sm:translate-x-0 r rounded-tl-[18px] rounded-tr-[18px] sm:rounded-tr-none 
          flex flex-col justify-between">
    {/* Header */}
    <div>
      <div className="flex justify-between items-center mb-6 p-6 bg-tables">
        <div>
          <h3 className="text-[20px] font-normal text-[#050013]">Edit Profile</h3>
        </div>
        <div className="flex items-center gap-3">
          <button
            onClick={() => setIsRightPanel(false)}
            className="text-gray-400 hover:text-black text-xl"
          >
            <FiX />
          </button>
        </div>
      </div>

      {/* Form Fields */}
      <div className="px-6 space-y-6">
        {/* Full Name */}
        <div className="w-full border border-gray-300 rounded-lg px-4 pt-3 pb-2 relative">
          <p className="text-[13px] text-[#76787A] mb-1 absolute bg-white top-[-10px]">Full Name</p>
          <input
            type="text"
            defaultValue="Rivka Frank"
            className="w-full text-[13px] text-[#050013] placeholder-[#050013] focus:outline-none"
            placeholder="Enter full name"
          />
        </div>

        {/* ID */}
        <div className="w-full border border-gray-300 rounded-lg px-4 pt-3 pb-2 relative">
          <p className="text-[13px] text-[#76787A] mb-1 absolute bg-white top-[-10px]">ID</p>
          <input
            type="text"
            defaultValue="DRV-20250123"
            className="w-full text-[13px] text-[#050013] placeholder-[#050013] focus:outline-none"
            placeholder="Enter ID"
          />
        </div>

        {/* Upload Document */}
<div
                                                className="shadow-none p-4 mt-3 mb-1 bg-white border border-gray-200 rounded-lg dark:bg-gray-800 dark:border-gray-700">
                                                <div
                                                    className="flex items-center justify-center w-full">
                                                    <label
                                                        className="flex flex-col items-center justify-center w-full  border-2 border-gray-300 border-dashed rounded-lg cursor-pointer py-8
">
                                                        <div
                                                            className="flex items-center justify-center pt-5 pb-6">
                                                                 <Cloud/>
                                                            <p className="text-dark-grey dark:text-gray-400 dark:text-gray-400">
                                                                <span className="px-3 text-[13px]">Click or drag file to this area to upload</span>
                                                            </p>
                                                            
                                                        </div>
                                                        <input id="dropzone-file" type="file"
                                                            className="hidden" />
                                                    </label>
                                                </div>
                                            </div><p className="text-[#76787A] text-[13px]">Formats accepted are PNG & JPG</p>
      </div>
    </div>

    {/* Bottom CTA */}
    <div className="p-6 border-t flex justify-end">
      <button className="py-3 px-6 w-full sm:w-auto rounded-full bg-[#3707EF] hover:bg-[#3e1ed0] text-white text-[13px] font-semibold">
        Save
      </button>
    </div>
  </div>
</div>


      }
    </div>
  );
}
