"use client";

import { useEffect } from "react";
import { useHeaderTitle } from "@/context/HeaderTitleContext";
import { IoCloseOutline } from "react-icons/io5";
import { GoArrowUp } from "react-icons/go";
import { GrDirections } from "react-icons/gr";
import { CgCornerDoubleUpLeft } from "react-icons/cg";



export default function StartRidePage() {
  const { setTitle } = useHeaderTitle();

  useEffect(() => {
    setTitle("Start Ride");
  }, []);

  return (
    <div className="max-w-7xl mx-auto">
      <div className="bg-white flex flex-col md:flex-row overflow-hidden shadow-lg">
        {/* Right Panel: Map */}
        <div className="w-full relative flex items-center justify-center bg-gray-100">
          {/* Map Image */}
          <img
            src="/images/map.svg"
            alt="Route Map"
            className="w-full h-full object-cover"
          />

          {/* Top Left: Direction */}
          <div className="absolute top-4 left-4 bg-white shadow-md rounded-xl py-5 px-6 text-sm flex items-center gap-2">
            <GoArrowUp className="text-[#050013]" size={22} />
            <div className="flex flex-col leading-tight">
             <span className="text-[#76787A] text-[13px] font-normal">Towards</span>
             <span className="font-normal text-[#050013] text-[15px]">Spuistraat City Rd</span>
            </div>
          </div>

          {/* Then Button */}
          <div className="absolute top-[28%] sm:top-[12%] left-4">
          <button className="bg-black text-white text-[13px] px-5 py-2 rounded-full shadow-md flex items-center gap-2">
  <CgCornerDoubleUpLeft size={25} />
  Then
</button>
          </div>

          <div className="absolute bg-white shadow-lg px-4 py-3 flex items-center justify-evenly w-full left-0 bottom-0 rounded-none
    sm:top-[20%] sm:left-4 sm:rounded-xl sm:justify-start sm:gap-5 sm:w-auto sm:bottom-auto">
           <IoCloseOutline className="border rounded-full text-[#050013] p-[5px]" size={40} />
            <div className="text-left">
              <p className="text-[24px] font-semibold text-btn leading-tight">35 min</p>
              <p className="text-[15px] text-[#76787A]">21 Km • 14:42</p>
            </div>
            <GrDirections className="text-[#050013] border rounded-full p-[10px] ml-2" size={40} />
            </div>

            <div className="fixed sm:absolute left-4 bottom-[10%] bg-white shadow-lg rounded-full p-3 flex items-center gap-5">
            <div className="text-center text-[13px]">
              <p className="font-semibold text-btn leading-tight">--</p>
              <p className="text-[#76787A]">Km/h</p>
            </div>
            </div>

        </div>
      </div>
    </div>
  );
}
