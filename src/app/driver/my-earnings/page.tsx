"use client";

import React, { useEffect } from 'react';
import { useHeaderTitle } from "@/context/HeaderTitleContext";
import { FaArrowUp, FaPhone, FaEuroSign } from "react-icons/fa";
import { BiPhoneCall } from "react-icons/bi";
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, Legend} from "recharts";

const data = [
    { month: 'Jan', income: 30 },
    { month: 'Feb', income: 100 },
    { month: 'Mar', income: 120 },
    { month: 'Apr', income: 40 },
    { month: 'May', income: 220 },
    { month: 'Jun', income: 300 },
    { month: 'Jul', income: 280 },
    { month: 'Aug', income: 240 },
    { month: 'Sep', income: 260 },
    { month: 'Oct', income: 210 },
    { month: 'Nov', income: 90 },
    { month: 'Dec', income: 320 },
  ];

const payoutHistory = [
  { date: '15-01-2025', time: '11:27', tripId: '€22.00', amount: '98%', charges: 'Fee - € 5.00' },
  { date: '10-01-2025', time: '12:00', tripId: '€22.00', amount: '98%', charges: 'Fee - € 5.00' },
  { date: '09-01-2025', time: '10:25', tripId: '€22.00', amount: '98%', charges: 'Fee - € 5.00' },
  { date: '08-01-2025', time: '11:27', tripId: '€22.00', amount: '98%', charges: 'Fee - € 5.00' },
  { date: '07-01-2025', time: '10:25', tripId: '€22.00', amount: '98%', charges: 'Fee - € 5.00' },
  { date: '06-01-2025', time: '11:27', tripId: '€22.00', amount: '98%', charges: 'Fee - € 5.00' },
  { date: '04-01-2025', time: '11:27', tripId: '€22.00', amount: '98%', charges: 'Fee - € 5.00' }
];

export default function EarningsDashboard() {
  const { setTitle } = useHeaderTitle();

  useEffect(() => {
    setTitle("Earnings Dashboard");
  }, []);

  return (
    <div className="relative min-h-screen bg-white p-4 md:p-6 font-sans text-gray-800">
 {/* Main Layout Starts */}
  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
  {/* LEFT SIDE - Select + Total Earning + Trip + Bonus */}
  <div className="flex flex-col gap-4">
  {/* SELECT DROPDOWN */}
  <div className="justify-between sm:justify-end flex">
    <p className="text-[16px] text-[#050013] font-normal block sm:hidden">My Earning</p>
    <select className="text-[16px] sm:text-[13px] text-[#76787A] focus:!border-0 focus:outline-none">
      <option>Daily</option>
      <option>Weekly</option>
      <option>Monthly</option>
    </select>
  </div>

  {/* Earnings Box Wrapper */}
  <div className="flex flex-col gap-4 lg:flex-row lg:items-stretch h-full">
    {/* Total Earning */}
    <div className="w-full lg:w-1/2">
      <div className="blue-gradient text-white rounded-2xl p-6 h-full flex sm:block justify-between align-center">
        <div className="flex items-center mb-0 lg:mb-[28%]">
          <p className="px-5 py-[10px] text-[30px] rounded-full bg-[#251AA4]">€</p>
        </div>
        <div>
          <p className="text-white text-sm">Total Earning</p>
          <p className="text-2xl font-semibold pt-1 sm:pb-6">€ 25.00</p>
        </div>
        <div className="sm:block hidden">
          <p className="flex items-center text-green-300 text-sm">
            <span className="mr-3 bg-active-status text-white p-[6px] rounded-[22px]"><FaArrowUp /></span>
            <span className="text-green-600 contents">8.5%</span>
            <span className="text-white ml-1 contents"> Up from yesterday</span>
          </p>
        </div>
      </div>
    </div>

    {/* Trip + Bonus Wrapper */}
    <div className="flex flex-row gap-4 w-full lg:flex-col lg:w-1/2">
      {/* Trip Earning */}
      <div className="w-1/2 lg:w-full bg-white border rounded-2xl text-left">
        <div className="text-sm text-[#76787A] px-4 pt-6">Trip Earning</div>
        <div className="text-xl font-semibold py-3 px-4 text-[#050013]">€ 5.00</div>
        <div>
          <p className="flex items-center text-green-300 text-sm bg-cstm-grey py-2 px-4 rounded-b-[15px]">
            <span className="mr-3 bg-active-status text-white p-[5px] rounded-[22px]"><FaArrowUp /></span>
            <span className="text-green-600 contents">8.5%</span>
            <span className="text-[#050013] ml-1 contents"> Up from yesterday</span>
          </p>
        </div>
      </div>

      {/* Bonus Box */}
      <div className="w-1/2 lg:w-full bg-white border rounded-2xl text-left">
        <div className="text-sm text-[#76787A] px-4 pt-6">Bonus</div>
        <div className="text-xl font-semibold py-3 px-4 text-[#050013]">€ 2.00</div>
        <div>
          <p className="flex items-center text-green-300 text-sm bg-cstm-grey py-2 px-4 rounded-b-[15px]">
            <span className="mr-3 bg-active-status text-white p-[5px] rounded-[22px]"><FaArrowUp /></span>
            <span className="text-green-600 contents">8.5%</span>
            <span className="text-[#050013] ml-1 contents"> Up from yesterday</span>
          </p>
        </div>
      </div>
    </div>
  </div>
</div>


  {/* RIGHT SIDE - Graph */}
  <div className="bg-white border rounded-2xl shadow h-full flex flex-col justify-start">
          <div className="flex items-start justify-between bg-cstm-grey py-3 px-6 rounded-t-[15px]">
            <h2 className="text-[16px] text-[#050013] font-normal">Incentive Progress</h2>
            <select className="px-3 py-1 text-sm text-[#76787A]">
              <option>Week</option>
              <option>Month</option>
            </select>
          </div>
          <div className="text-[13px] bg-[#FFF7EB] px-4 py-3 rounded-lg text[#050013] px-4 py-3 text-center font-normal m-4">
            🥳 Complete 12 more rides to earn $150 bonus
          </div>
          <div className="graph-earning flex-1 rounded-lg text-gray-400 text-sm min-h-[150px]">
       <ResponsiveContainer width="100%" height={200}>
        <LineChart data={data} margin={{ top: 10, right: 20, left: 0, bottom: 30 }}>
          <CartesianGrid strokeDasharray="4 4" vertical={false} stroke="#E5E7EB" />
          <XAxis dataKey="month" axisLine={false} tickLine={false} tick={{ dy: 20}} />
          <YAxis tickFormatter={(v) => `€${v}`} axisLine={false} tickLine={false} />
          <Tooltip formatter={(v) => `€${v}`} />
          {/* <Legend /> */}
          <Legend
            verticalAlign="bottom"
            align="center"
            content={({ payload }) => (
                <ul>
                {payload.map((entry, index) => (
                    <li key={`item-${index}`} className="hidden lg:flex items-center justify-center gap-2 text-sm text-gray-600">
                    {/* Replace icon with dot */}
                    <span
                        className="w-2 h-2 rounded-full"
                        style={{ backgroundColor: entry.color }}
                    ></span>
                    <span>{entry.value}</span>
                    </li>
                ))}
                </ul>
            )}
            />
          <Line type="monotone" dataKey="income" stroke="#251AA1" strokeWidth={3} dot={false}/>
        </LineChart>
       </ResponsiveContainer>
          </div>
        </div>
</div>

       {/* Payout History Section */}
        <div className="bg-white mt-10 rounded-2xl shadow overflow-x-auto">
        <div className="p-4 border-b text-lg font-normal bg-tables">Payout History</div>
        <table className="min-w-full divide-y">
          <thead className="text-sm font-normal text-[#76787A]">
            <tr>
              <th className="px-6 py-3 text-left font-medium">Date</th>
              <th className="px-6 py-3 text-left font-medium">Time</th>
              <th className="px-6 py-3 text-left font-medium">Trip ID</th>
              <th className="px-6 py-3 text-left font-medium">Amount</th>
              <th className="px-6 py-3 text-left font-medium">Other charges</th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200 text-sm text-[#050013]">
            {payoutHistory.map((entry, idx) => (
              <tr key={idx}>
                <td className="px-6 py-4 whitespace-nowrap">{entry.date}</td>
                <td className="px-6 py-4 whitespace-nowrap">{entry.time}</td>
                <td className="px-6 py-4 whitespace-nowrap">{entry.tripId}</td>
                <td className="px-6 py-4 whitespace-nowrap">{entry.amount}</td>
                <td className="px-6 py-4 whitespace-nowrap">{entry.charges}</td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Sticky Call Icon */}
      <div className="fixed bottom-6 right-6 z-50">
        <button className="w-12 h-12 rounded-full bg-black text-white flex items-center justify-center shadow-lg hover:bg-gray-800">
          <BiPhoneCall className="text-xl h-[25px]" />
        </button>
      </div>
    </div>
  );
}
