"use client";

import { useEffect, useState } from "react";
import { useHeaderTitle } from "@/context/HeaderTitleContext";
import { FaStar } from "react-icons/fa";
import { FiChevronDown } from "react-icons/fi";
import { PiMapPinLight } from "react-icons/pi";
import { RxStarFilled } from "react-icons/rx";
import { LuFlagTriangleRight } from "react-icons/lu";


export default function StartRidePage() {
  const { setTitle } = useHeaderTitle();
 const [isRightPanel, setIsRightPanel] = useState<boolean>(false);

  useEffect(() => {
    setTitle("Start Ride");
  }, []);

  return (
    <div className="max-w-7xl mx-auto sm:p-4">
      <div className="bg-white rounded-2xl border flex flex-col md:flex-row overflow-hidden">
        {/* Left Panel */}
        <div className="hidden sm:block w-full md:w-1/2 space-y-6 relative">
          <div className="space-y-4 p-6">
            <div className="flex items-start gap-3">
              <img
                src="/images/avatar.jpg"
                alt="Rider"
                className="w-12 h-12 rounded-full object-cover"
              />
              <div>
                <div className="flex items-center gap-2">
                  <p className="font-medium text-[#050013] text-[16px]">Rivka Frank</p>
                  <div className="flex items-center text-[15px] text-yellow-500">
                  <RxStarFilled />
                    <span className="ml-1 text-[15px] text-[#050013] font-normal">4.5</span>
                  </div>
                </div>


                <div className="relative w-40">
                <select className="appearance-none border-0 rounded-lg text-[12px] text-[#76787A] focus:outline-none">
                  <option>Fare Estimate</option>
                  <option>Premium</option>
                </select>
                <span className="absolute top-[14px] right-[30%] p-[2px] bg-tables rounded-[12px] -translate-y-1/2 pointer-events-none">
                  <FiChevronDown className="text-[#050013]" />
                </span>
              </div>

              <div>
              <p className="text-[20px] font-medium text-[#050013]">
                € 35.00
              </p>
            </div>
              </div>
            </div>
          </div>

         
          <div className="border-t p-6">
          <div className="relative">
            <p className="text-[13px] font-normal text-[#76787A] mb-4">This ride has 5 Stops</p>
            <div className="space-y-0">
     
              <div className="flex items-start gap-3 relative">
                <div className="flex flex-col items-center">
                  <PiMapPinLight className="text-[#6F3FF5] p-[4px] rounded-full bg-blue-50 z-10" size={25} />
                  <div className="h-6 border-l-2 border-dashed border-gray-300" />
                </div>
                <p className="text-[13px] font-normal text-[#050013]">288–292, Spuistraat City</p>
              </div>
            
              <div className="flex items-start gap-3 relative">
                <div className="flex flex-col items-center">
                  <LuFlagTriangleRight className="text-[#6F3FF5] p-[4px] rounded-full bg-blue-50 z-10" size={25} />
                </div>
                <p className="text-[13px] font-normal text-[#050013]">1000 AP, Schipol Airport...</p>
              </div>
            </div>
 
            <div className="absolute top-0 right-0 bg-tables px-4 py-2 rounded-lg text-center">
              <p className="text-[13px] font-normal text-[#76787A]">ETA</p>
              <p className="text-[16px] font-medium text-[#050013]">18 min</p>
            </div>
          </div>
          </div>
        
          <div className="p-6">
          <button onClick={() => setIsRightPanel(true)} className="w-full py-3 bg-active-status hover:bg-[#00b24c] text-white font-medium text-sm rounded-full transition-all">
            Start ride
          </button>
          </div>
         </div>

        {/* Right Panel */}
        <div className="w-full md:w-1/2 relative bg-gray-100 flex flex-col items-center justify-start">
          {/* Map Image */}
          <img
            src="/images/map.png"
            alt="Route Map" 
            className="w-full object-cover"
          />
        </div>
      </div>


   {isRightPanel &&
        <div className="sm:hidden fixed inset-0 z-[999] flex justify-end bg-black/30 transition-opacity duration-300 ease-in-out">
        <div className="w-full sm:max-w-md bg-white shadow-lg fixed bottom-0 h-[50%] w-full bg-white rounded-tl-[15px] rounded-tr-[15px]">
          
        {/* start  */}
        <div className="w-full space-y-6 relative">
          <div className="p-6 pb-4 mb-0">
            <div className="flex items-start gap-3">
              <img
                src="/images/avatar.jpg"
                alt="Rider"
                className="w-12 h-12 rounded-full object-cover"
              />
              <div>
                <div className="flex items-center gap-2">
                  <p className="font-medium text-[#050013] text-[16px]">Rivka Frank</p>
                  <div className="flex items-center text-[15px] text-yellow-500">
                  <RxStarFilled />
                    <span className="ml-1 text-[15px] text-[#050013] font-normal">4.5</span>
                  </div>
                </div>
                <div className="relative w-40">
                <select className="appearance-none border-0 rounded-lg text-[12px] text-[#76787A] focus:outline-none">
                  <option>Fare Estimate</option>
                  <option>Premium</option>
                </select>
                <span className="absolute top-[14px] right-[30%] p-[2px] bg-tables rounded-[12px] -translate-y-1/2 pointer-events-none">
                  <FiChevronDown className="text-[#050013]" />
                </span>
              </div>

              <div>
              <p className="text-[20px] font-medium text-[#050013]">
                € 35.00
              </p>
            </div>
              </div>
            </div>
          </div>
          <div className="border-t p-6 pb-0 mb-0">
          <div className="relative">
            <p className="text-[13px] font-normal text-[#76787A] mb-4">This ride has 5 Stops</p>
            <div className="space-y-0">
     
              <div className="flex items-start gap-3 relative">
                <div className="flex flex-col items-center">
                  <PiMapPinLight className="text-[#6F3FF5] p-[4px] rounded-full bg-blue-50 z-10" size={25} />
                  <div className="h-6 border-l-2 border-dashed border-gray-300" />
                </div>
                <p className="text-[13px] font-normal text-[#050013]">288–292, Spuistraat City</p>
              </div>
            
              <div className="flex items-start gap-3 relative">
                <div className="flex flex-col items-center">
                  <LuFlagTriangleRight className="text-[#6F3FF5] p-[4px] rounded-full bg-blue-50 z-10" size={25} />
                </div>
                <p className="text-[13px] font-normal text-[#050013]">1000 AP, Schipol Airport...</p>
              </div>
            </div>
 
            <div className="absolute top-0 right-0 bg-tables px-4 py-2 rounded-lg text-center">
              <p className="text-[13px] font-normal text-[#76787A]">ETA</p>
              <p className="text-[16px] font-medium text-[#050013]">18 min</p>
            </div>
          </div>
          </div>
        
          <div className="p-6">
          <button onClick={() => setIsRightPanel(true)} className="w-full py-3 bg-active-status hover:bg-[#00b24c] text-white font-medium text-sm rounded-full transition-all">
            Start ride
          </button>
          </div>
         </div>
        {/* end  */}
  
  
        </div>
      </div>
      }

    </div>
  );
}