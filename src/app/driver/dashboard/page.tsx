"use client";

import { useEffect } from "react";
import { useHeaderTitle } from "@/context/HeaderTitleContext";
import { FaArrowUp } from "react-icons/fa";
import { RxStarFilled } from "react-icons/rx";
import {
  <PERSON><PERSON>hart,
  Line,
  XAxis,
  <PERSON>Axis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  Legend,
} from "recharts";
import Image from "next/image";
import Link from "next/link";

const data = [
  { month: "Jan", income: 30 },
  { month: "Feb", income: 100 },
  { month: "Mar", income: 120 },
  { month: "Apr", income: 40 },
  { month: "May", income: 220 },
  { month: "Jun", income: 300 },
  { month: "Jul", income: 280 },
  { month: "Aug", income: 240 },
  { month: "Sep", income: 260 },
  { month: "Oct", income: 210 },
  { month: "Nov", income: 90 },
  { month: "Dec", income: 320 },
];

export default function DashboardPage() {
  const { setTitle } = useHeaderTitle();

  useEffect(() => {
    if (setTitle) setTitle("Driver Dashboard");
  }, [setTitle]);

  return (
    <div className="p-6 space-y-6">
 
 {/* Main Layout Starts */}
  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
  {/* LEFT SIDE - Select + Total Earning + Trip + Bonus */}
  <div className="flex flex-col gap-4">
    
    {/* SELECT DROPDOWN */}
    <div className="justify-between sm:justify-end flex">
      <p className="text-[16px] text-[#050013] font-normal block sm:hidden">My Earning</p>
      <select className="text-[16px] sm:text-[13px] text-[#76787A] focus:!border-0 focus:outline-none">
        <option>Daily</option>
        <option>Weekly</option>
        <option>Monthly</option>
      </select>
    </div>
    
    {/* Earnings Boxes */}
    {/* <div className="flex flex-col lg:h-full lg:flex-row lg:items-stretch gap-4"> */}
    <div className="flex flex-row items-stretch h-full gap-4">
      {/* Gradient Card - Total Earning */}
      <div className="flex flex-col w-full lg:w-1/2 justify-end">
        <div className="blue-gradient text-white rounded-2xl p-6 h-full">
          <div className="flex items-center mb-[30%]">
            <p className="bg-blue px-5 py-[10px] text-[30px] rounded-full bg-[#251AA4]">€</p>
          </div>
          <div>
          <p className="text-white text-sm">Total Earning</p>
          <p className="text-2xl font-semibold pt-1 pb-6">€ 25.00</p>
          </div>

          <div>
            <p className="flex items-center text-green-300 text-sm">
              <span className="mr-3 bg-active-status text-white p-[6px] rounded-[22px]"><FaArrowUp /></span>
              <span className="text-green-600 contents">8.5%</span>
              <span className="text-white ml-1 contents"> Up from yesterday</span>
            </p>
          </div>
        </div>
      </div>

      {/* Trip + Bonus */}
      <div className="flex flex-col w-full lg:w-1/2 gap-4">
        {/* Trip Earning */}
        <div className="bg-white border rounded-2xl text-left">
          <div className="text-sm text-[#76787A] px-4 pt-6">Trip Earning</div>
          <div className="text-xl font-semibold py-3 px-4 text-[#050013]">€ 5.00</div>
          <div>
            <p className="flex items-center text-green-300 text-sm bg-cstm-grey py-2 px-4 rounded-b-[15px]">
              <span className="mr-3 bg-active-status text-white p-[5px] rounded-[22px]"><FaArrowUp /></span>
              <span className="text-green-600 contents">8.5%</span>
              <span className="text-[#050013] ml-1 contents"> Up from yesterday</span>
            </p>
          </div>
        </div>

        {/* Bonus Box */}
        <div className="bg-white rounded-2xl border">
          <div className="text-sm text-[#76787A] px-4 pt-6">Bonus</div>
          <div className="text-xl font-semibold py-3 px-4 text-[#050013]">€ 2.00</div>
          <div>
            <p className="flex items-center text-green-300 text-sm bg-cstm-grey py-2 px-4 rounded-b-[15px]">
              <span className="mr-3 bg-active-status text-white p-[5px] rounded-[22px]"><FaArrowUp /></span>
              <span className="text-green-600 contents">8.5%</span>
              <span className="text-[#050013] ml-1 contents"> Up from yesterday</span>
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>

  {/* RIGHT SIDE - Graph */}
  <div className="bg-white border rounded-2xl shadow h-full flex flex-col justify-start">
    <div className="flex items-start justify-between bg-cstm-grey py-3 px-6 rounded-t-[15px]">
      <h2 className="text-[16px] text-[#050013] font-normal">Income</h2>
    </div>

    <div className="flex-1 rounded-lg text-gray-400 text-sm min-h-[150px]">
      <ResponsiveContainer width="100%" height={200}>
        <LineChart data={data} margin={{ top: 10, right: 20, left: 0, bottom: 30 }}>
          <CartesianGrid strokeDasharray="4 4" vertical={false} stroke="#E5E7EB" />
          <XAxis dataKey="month" axisLine={false} tickLine={false} tick={{ dy: 20 }} />
          <YAxis tickFormatter={(v) => `€${v}`} axisLine={false} tickLine={false} />
          <Tooltip formatter={(v) => `€${v}`} />
          <Legend
            verticalAlign="bottom"
            align="center"
            content={({ payload }) => (
              <ul>
                {payload.map((entry, index) => (
                  <li key={`item-${index}`} className="hidden lg:flex items-center justify-center gap-2 text-sm text-gray-600">
                    <span className="w-2 h-2 rounded-full" style={{ backgroundColor: entry.color }}></span>
                    <span>{entry.value}</span>
                  </li>
                ))}
              </ul>
            )}
          />
          <Line type="monotone" dataKey="income" stroke="#251AA1" strokeWidth={3} dot={false} />
        </LineChart>
      </ResponsiveContainer>
    </div>
  </div>
</div>


      {/* Row 3: Trip History & Rating */}
      <div className="grid grid-cols-1 xl:grid-cols-2 gap-6">
        {/* Trip History */}
        <div className="bg-white rounded-2xl border">
          <div className="flex justify-between items-center px-6 py-4 bg-gray-50 mb-6 rounded-t-2xl">
            <h3 className="text-[16px] text-[#050013] font-normal">My Trip History</h3>
            <select className="text-[16px] sm:text-[13px] text-[#76787A] focus:!border-0 focus:outline-none">
              <option>Weekly</option>
              <option>Monthly</option>
            </select>
          </div>
          <div>
            <div className="relative w-1/3 mx-auto border border-dashed rounded-full" style={{ borderColor: '#D6D5D5' }}>
              <svg viewBox="0 0 36 36">
                <path className="text-red-500" strokeDasharray="100, 100" d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831" fill="none" stroke="currentColor" strokeWidth="2" />
                <path className="text-green-500" strokeDasharray="80, 100" d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831" fill="none" stroke="currentColor" strokeWidth="2" />
              </svg>
              <div className="absolute inset-0 flex items-center justify-center text-xl font-bold">
                <div className="text-center">
                  <p className="text-[10px] sm:text-[13px] font-normal text-[#76787A]">Total Trips</p>
                  <p className="text-3xl">100</p>
                </div>
              </div>
            </div>
            <div className="p-6">
              <p className="border-b pt-4 pb-2 text-[13px] text-[#76787A] flex justify-between">Completed Trips<span className="text-green-600 text-[18px] font-medium">80</span></p>
              <p className="pt-2 text-[13px] text-[#76787A] flex justify-between">Canceled Trips<span className="text-red-500 text-[18px] font-medium">20</span></p>
            </div>
          </div>
        </div>

        {/* Rating */}
        <div className="bg-white rounded-2xl border">
          <h3 className="flex justify-between items-center px-6 py-4 bg-gray-50 mb-6 rounded-t-2xl">
            My Rating & Review
          </h3>
          <div className="flex flex-col items-center py-6">
            <Image
              src="/images/avatar.jpg"
              width={64}
              height={64}
              alt="Profile"
              className="rounded-full mb-2"
            />
            <p className="text-xl font-medium">4.5</p>
            <div className="flex text-yellow-500 my-3">
              {[...Array(4)].map((_, i) => (
                <RxStarFilled className="w-6 h-6" key={i} />
              ))}
              <RxStarFilled className="text-gray-300 w-6 h-6" />
            </div>
            <Link href="/driver/ratings">
              <button className="border border-gray-300 px-5 py-2 rounded-full text-sm hover:bg-gray-100">
                View All
              </button>
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}
