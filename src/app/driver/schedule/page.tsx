"use client";

import React, { useState } from "react";
import {
  Tab, TabGroup, TabList, TabPanel, TabPanels
} from "@headlessui/react";
import { PiCalendarDot, PiClockCounterClockwiseLight } from "react-icons/pi";
import { FiMic, FiCamera, FiX } from "react-icons/fi";
import { motion } from 'framer-motion';
import { TfiCheckBox } from "react-icons/tfi";
import { IoIosArrowForward, IoIosSearch } from "react-icons/io";
import { TbWheelchair, TbMap2, TbHelpTriangle, TbChevronDown,TbMoonStars, TbSunHigh, TbFilter, TbMapPin
} from "react-icons/tb";
import { LuPhone, LuMessageSquareMore, LuUser, LuFlagTriangleRight } from "react-icons/lu";
import { FaPhone, FaCommentDots, Fa<PERSON>ser, FaWheelchair, FaLocation<PERSON>rrow, FaPlus } from "react-icons/fa";
import { GrLocation } from "react-icons/gr";
import { CiClock2 } from "react-icons/ci";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import { SlArrowLeft } from "react-icons/sl";
import { LuEuro } from "react-icons/lu";
import Link from "next/link";


export default function SchedulePage() {
  const [selectedTrip, setSelectedTrip] = useState("RT2345");
  const [stepSlide, setStepSlide] = useState(0);
  const [availabilityModal, setAvailabilityModal] = useState<boolean>(false);
  const [isMobileView, setIsMobileView] = useState(false);
  const handleDetailView = () => {
    if (window.innerWidth < 640) {
      setIsMobileView(true);
      setStepSlide(0);
    }
  };
  
  const handleMapView = () => {
    if (window.innerWidth < 640) {
      setIsMobileView(true);
      setStepSlide(1);
    }
  };
  
  const handleBackToSchedule = () => {
    if (window.innerWidth < 640) {
      setIsMobileView(false);
    }
  };
  React.useEffect(() => {
    const updateView = () => setIsMobileView(window.innerWidth < 640 && isMobileView);
    window.addEventListener("resize", updateView);
    return () => window.removeEventListener("resize", updateView);
  }, [isMobileView]);
  



  const scheduleData = [
    {
      id: "RT2345",
      time: "10:00 AM",
      route: "Singel 230",
      routeId: "#RT2345",
      totalStops: "03",
      passengers: "04",
      date: "24-01-2025",
      vehicle: {
        license: "XYZ-1234",
        model: "Toyota Prius",
        type: "Sedan"
      },
      passengerDetails: [
        ["Henk Maarten", "Emily Chain"],
        ["Robert Martin", "Anna Jame"]
      ],
      steps: [
        {
          time: "10:00 AM",
          label: "Pick Up 1",
          name: "Henk Maarten",
          address: "Nieuwezijds Voorburgwal 102",
          icons: [1, 1, 1],
          color: "bg-green-200 text-green-700",
          dot: "border-green-500",
          passengers: 1,
          wheelchairs: 1,
        },
        {
          time: "10:15 AM",
          label: "Pick Up 2",
          name: "Emily Chain",
          address: "Prinsengracht 78",
          icons: [1],
          color: "bg-purple-100 text-purple-600",
          dot: "border-purple-500",
          passengers: 1,
          wheelchairs: 1,
        },
        {
          time: "10:30 AM",
          label: "Drop Off 1",
          name: "Henk Maarten",
          address: "Reguliersdwarsstraat 54",
          icons: [1, 1, 1],
          color: "bg-green-200 text-green-700",
          dot: "border-green-600",
          passengers: 1,
          wheelchairs: 1,
        },
        {
          time: "11:00 AM",
          label: "Pick Up 3",
          name: "Robert Martin",
          address: "",
          icons: [1, 1, 1],
          color: "bg-rose-100 text-rose-400",
          dot: "border-rose-500",
          passengers: 1,
          wheelchairs: 1,
        }
      ]
    },
    {
      id: "RT2346",
      time: "12:00 PM",
      route: "Schiphol Airport",
      routeId: "#RT2346",
      totalStops: "02",
      passengers: "04",
      date: "20-11-2025",
      vehicle: {
        license: "ABC-98876",
        model: "Honda Amaze",
        type: "Sedan"
      },
      passengerDetails: [
        ["John Doe", "Emily Doe"],
        ["Robert Luthar", "Ana Jame"]
      ],
      steps: [
        {
          time: "12:03 PM",
          label: "Pick Up 9",
          name: "Henry Ford",
          address: "Portal Street 192",
          icons: [1, 1, 1],
          color: "bg-green-200 text-green-700",
          dot: "border-green-500",
          passengers: 1,
          wheelchairs: 1,
        },
        {
          time: "10:15 AM",
          label: "Pick Up 2",
          name: "Emily Chain",
          address: "Prinsengracht 78",
          icons: [1],
          color: "bg-purple-100 text-purple-600",
          dot: "border-purple-500",
          passengers: 1,
          wheelchairs: 1,
        },
        {
          time: "10:30 AM",
          label: "Drop Off 1",
          name: "Henk Maarten",
          address: "Reguliersdwarsstraat 54",
          icons: [1, 1, 1],
          color: "bg-green-200 text-green-700",
          dot: "border-green-600",
          passengers: 1,
          wheelchairs: 1,
        },
        {
          time: "11:00 AM",
          label: "Pick Up 3",
          name: "Robert Martin",
          address: "",
          icons: [1, 1, 1],
          color: "bg-rose-100 text-rose-400",
          dot: "border-rose-500",
          passengers: 1,
          wheelchairs: 1,
        }
      ]
    }
  ];

const selected = scheduleData.find((trip) => trip.id === selectedTrip);
// Availabilty start
const days = ["MON", "TUE", "WED", "THU", "FRI", "SAT", "SUN"];
const times = [
  "6 AM", "7 AM", "8 AM", "9 AM", "10 AM", "11 AM",
  "12 PM", "1 PM", "2 PM", "3 PM", "4 PM", "5 PM",
  "6 PM", "7 PM", "8 PM"
];

console.log("Hello There", selected);

const [rideRequestModal, setRideRequestModal] = useState(false);
React.useEffect(() => {
  const timer = setTimeout(() => {
    setRideRequestModal(true);
  }, 5000); 

  return () => clearTimeout(timer); // cleanup on unmount
}, []);

// availability end 
  return (
    <div className="min-h-screen">
      <div className="max-w-7xl mx-auto px-6 py-2">
        <TabGroup>
          <TabList className="flex gap-6 px-6 py-4 bg-white">
            <Tab className={({ selected }) => `flex items-center space-x-2 pb-3 border-b-2 text-sm font-medium outline-none ${selected ? 'border-blue-600 text-purple' : 'border-transparent text-[#76787A]'}`}> <PiCalendarDot className="w-[20px] h-[20px]" /> <span>My Schedule</span> </Tab>
            <Tab className={({ selected }) => `flex items-center space-x-2 pb-3 border-b-2 text-sm font-medium outline-none ${selected ? 'border-blue-600 text-purple' : 'border-transparent text-[#76787A]'}`}> <TfiCheckBox className="w-[20px] h-[20px]" /> <span>My Availability</span> </Tab>
          </TabList>

          <TabPanels>
          <TabPanel>
  {/* Mobile View Handling */}
  <div className="sm:hidden">


{/* start test  */}
<div>
          <div className="flex items-center justify-between my-6">
        <h2 className="text-[16px] font-normal text-[#050013]">My Schedule</h2>
        <button type="button" className="flex items-center gap-2 py-2 px-4 text-xs font-medium text-[#76787A] bg-white rounded-full shadow-[0px_1px_1px_0px_#0000001A] hover:bg-gray-100 hover:text-blue-700">
        <TbFilter size={16}/>
          Filters
        </button>
      </div>
          </div>
        <div className="flex items-center bg-white rounded py-3 px-4 text-center text-sm 
          shadow-[0px_2px_15px_0px_#1D24610D] gap-4 sm:gap-0">
          <div className="bg-cstm-grey px-6 py-4 rounded text-purple font-semibold w-full sm:w-auto text-center">
          <div className="text-[14px] font-normal text-[#050013]">Today</div>
          <div className="text-[16px] font-semibold">24 Jan</div>
        </div>
        <div className="flex-1 px-6 py-4">
          <div className="text-[16px] font-semibold text-[#050013]">02</div>
          <div className="text-[12px] font-normal text-[#76787A]">Rides</div>
        </div>
        <div className="flex-1 px-6 py-4 border-l">
          <div className="text-[16px] font-semibold text-[#050013]">08</div>
          <div className="text-[12px] font-normal text-[#76787A]">Passengers</div>
        </div>
          </div>
{/* end test  */}

  {!isMobileView && (
<div className="space-y-6 mt-6">
      {scheduleData.map((trip) => (


        <div onClick={() => { setSelectedTrip(trip.id); handleDetailView(); }}>
          <div key={trip.id} className="flex items-start space-x-4 mb-4">
          <div className="w-20 text-right pt-1 text-sm text-[#76787A]">{trip.time}</div>
          <div onClick={() => setSelectedTrip(trip.id)}
            className={`flex-1 cursor-pointer rounded-xl border bg-white px-4 sm:px-6 py-4 transition ${selectedTrip === trip.id ? 'border-blue-600 bg-blue-50' : 'border-none hover:border-gray-300'}`}>
            <div className="flex justify-between items-center mb-4">
              <div>
                <h3 className="text-[14px] font-medium text-[#050013] mb-2">{trip.route}</h3>
                <p className="text-[14px] text-purple font-normal">{trip.routeId}</p>
              </div>
              <div className="h-8 w-8 flex items-center justify-center rounded-full bg-cstm-grey">
                <IoIosArrowForward className="text-gray-800 w-4 h-4" />
              </div>
            </div>
            <div className="flex justify-between items-center flex-wrap gap-4">
              <div>
                <p className="text-[13px] text-[#76787A]">Total Stops</p>
                <p className="text-[16px] font-semibold text-[#050013]">{trip.totalStops}</p>
              </div>
              <div>
                <p className="text-[13px] text-[#76787A]">Passengers</p>
                <p className="text-[16px] font-semibold text-[#050013]">{trip.passengers}</p>
              </div>
              <div className="flex items-center">
                <TbWheelchair className="text-[#76787A] pr-2" size={28}/>
                <button className={`px-4 py-2 rounded-full text-sm font-medium transition ${selectedTrip === trip.id ? 'bg-cstm-blue-700 text-white' : 'border border-btn text-purple hover:bg-blue-50'}`}>
                  View Details
                </button>
              </div>
            </div>
          </div>
        </div>
        </div>

      ))}


      
    </div>
  )}

  {isMobileView && stepSlide === 0 && selected && (
    <div className="space-y-4 mt-6">
      <div className="flex items-center">
        <SlArrowLeft
          onClick={handleBackToSchedule}
          className="text-xl text-gray-600 mr-3 cursor-pointer"
        />
        <h2 className="text-lg font-semibold text-[#1F1F1F]">{selected.route}</h2>
      </div>

     <div className="w-full bg-white overflow-y-auto custom-scrollbar p-4 sm:p-6">
      {selected && (
        <div className="space-y-6">
          {/* Top Section */}
          <div className="flex justify-between items-start">
            <div className="flex">
              {stepSlide === 1 && (
                <div className="text-[#76787A] cursor-pointer mr-2 pt-1">
                  <SlArrowLeft onClick={() => setStepSlide(0)} />
                </div>
              )}
              <div>
                <h2 className="text-[16px] font-medium mb-1 text-[#050013]">{selected.route}</h2>
                <p className="text-purple font-normal text-[14px]">{selected.routeId}</p>
              </div>
            </div>
            <div className="flex space-x-3">
              <button onClick={handleMapView} className={`w-10 h-10 rounded-full border flex items-center justify-center text-xl ${stepSlide === 1 ? 'text-white bg-cstm-blue-700' : 'text-purple border-gray-300'}`}>
                <TbMap2 />
              </button>
              <button className="w-10 h-10 rounded-full border border-gray-300 flex items-center justify-center text-purple text-xl">
                <TbHelpTriangle />
              </button>
            </div>
          </div>

          {/* Meta Info */}
          <div className="flex flex-wrap gap-x-4 gap-y-2 text-[13px] text-gray-700 border-b pb-4">
            <div className="text-[#76787A]">Date: <span className="text-[#050013] font-medium">{selected.date} {selected.time}</span></div>
            <div className="text-[#76787A] border-l pl-2">Stops: <span className="text-[#050013] font-medium">{selected.totalStops}</span></div>
            <div className="text-[#76787A] border-l pl-2 flex items-center gap-1">Passengers:
              <span className="inline-flex items-center text-[11px] justify-center w-6 h-6 bg-gradient-to-br from-[#18EC95] to-[#3324E3] text-white text-xs rounded-full">{selected.passengers}</span>
            </div>
          </div>

          {/* Vehicle Details */}
          <div className="bg-gray-50 rounded-xl p-6 space-y-4">
            <h3 className="text-[13px] font-medium text-[#76787A]">Vehicle Details</h3>
            <div className="grid grid-cols-2 sm:grid-cols-3 gap-4 text-sm text-gray-700">
              <div><span className="text-[#76787A]">License Plate:</span><br /><span className="font-medium text-[#050013]">"License"</span></div>
              <div><span className="text-[#76787A]">Model:</span><br /><span className="font-medium text-[#050013]">model</span></div>
              <div><span className="text-[#76787A]">Type:</span><br /><span className="font-medium text-[#050013]">type</span></div>
            </div>
          </div>

          {/* Passenger Details */}
          <div className="bg-gray-50 rounded-xl px-6 py-3">
            <h3 className="text-[13px] font-medium text-[#76787A] mb-5">Passenger Details</h3>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-y-3 text-sm text-gray-800">
              {selected.passengerDetails.flat().map((name, i) => (
                <div key={i} className="flex items-center justify-around">
                  <span className="text-[#050013]">{name}</span>
                  <div className="flex space-x-2">
                    <LuPhone className="text-[#76787A]" />
                    <LuMessageSquareMore className="text-[#76787A]" />
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Stepper */}
          <div className="space-y-10">
            {selected.steps.map((step, index) => (
              <div key={index} className="flex gap-4 relative">
                <div className="w-[80px] text-right text-xs text-gray-500 pt-1 hidden sm:block">
                  <div className="text-[#76787A] text-[13px]">{step.time}</div>
                  <div className="text-[#050013] text-[12px]">{step.type || "Depart"}</div>
                </div>
                <div className="relative flex flex-col items-center min-h-[60px]">
                  <div className={`w-4 h-4 rounded-full border-4 bg-white ${step.dot}`} />
                  {index !== selected.steps.length - 1 && (
                    <div className="absolute top-4 left-1/2 transform -translate-x-1/2 w-px h-[115px] border-l border-dashed border-gray-300" />
                  )}
                </div>
                <div className="flex-1">
                  <div className={`text-xs px-3 py-1.5 inline-block rounded-full font-medium mb-1 ${step.color}`}>
                    {step.label}
                  </div>
                  <div className="font-medium text-[#050013] text-[14px] mt-2 flex items-center gap-4">
                    {step.name}
                    {step.passengers !== undefined && (
                      <span className="flex items-center gap-1 text-[13px] text-[#050013] font-normal">
                        <LuUser className="text-xs" /> {step.passengers}
                      </span>
                    )}
                    {step.wheelchairs !== undefined && (
                      <span className="flex items-center gap-1 text-[13px] text-[#050013] font-normal">
                        <TbWheelchair className="text-xs" /> {step.wheelchairs}
                      </span>
                    )}
                  </div>
                  {step.address && (
                    <div className="text-[12px] text-[#030015] flex items-center gap-1 mt-1">
                      <span className="bg-blue-50 p-[6px] rounded-full text-[14px] mr-[5px]">
                        <GrLocation className="text-purple" />
                      </span>
                      {step.address}
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>

    </div>
  )}

  {isMobileView && stepSlide === 1 && (
    <div className="space-y-4">
      <div>
        {/* <SlArrowLeft
          onClick={() => setStepSlide(0)}
          className="text-xl text-gray-600 mr-3 cursor-pointer"
        /> */}
        {/* map view start  */}
        <div className="flex justify-between items-start mt-6">
            <div className="flex">
            <SlArrowLeft
          onClick={() => setStepSlide(0)}
          className="text-xl text-gray-600 mr-3 cursor-pointer"
        />
              <div>
                <h2 className="text-[16px] font-medium mb-1 text-[#050013]">{selected.route}</h2>
                <p className="text-purple font-normal text-[14px]">{selected.routeId}</p>
              </div>
            </div>
            <div className="flex space-x-3">
              <button onClick={handleMapView} className={`w-10 h-10 rounded-full border flex items-center justify-center text-xl ${stepSlide === 1 ? 'text-white bg-cstm-blue-700' : 'text-purple border-gray-300'}`}>
                <TbMap2 />
              </button>
              <button className="w-10 h-10 rounded-full border border-gray-300 flex items-center justify-center text-purple text-xl">
                <TbHelpTriangle />
              </button>
            </div>
          </div>
          <div className="flex flex-wrap gap-x-4 gap-y-2 text-[13px] text-gray-700 border-b mt-5 pb-4">
            <div className="text-[#76787A]">Date: <span className="text-[#050013] font-medium">{selected.date} {selected.time}</span></div>
            <div className="text-[#76787A] border-l pl-2">Stops: <span className="text-[#050013] font-medium">{selected.totalStops}</span></div>
            <div className="text-[#76787A] border-l pl-2 flex items-center gap-1">Passengers:
              <span className="inline-flex items-center justify-center text-[11px] w-6 h-6 bg-gradient-to-br from-[#18EC95] to-[#3324E3] text-white text-xs rounded-full">{selected.passengers}</span>
            </div>
          </div>
        {/* map view end  */}
      </div>

      <div className="w-full h-[400px] rounded-2xl overflow-hidden border border-gray-200 shadow">
        <img
          src="/images/map.jpg"
          alt="Map"
          className="w-full h-full object-cover"
        />
      </div>
    </div>
  )}
</div>


  {/* Desktop View */}
  <div className="hidden sm:block">
          <div className="relative w-full h-screen overflow-hidden bg-gray-100 border rounded-[15px]">
  <div
    className="flex h-full transition-transform duration-700 ease-in-out"
    style={{
      width: '150%',
      transform: `translateX(-${stepSlide * 33.3333}%)`,
    }}
  >
    {/* Schedule Panel */}
    <div className="w-full p-4 sm:p-6 bg-tables overflow-y-auto custom-scrollbar">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-[16px] font-normal text-[#050013]">My Schedule</h2>
        <button type="button" className="flex items-center gap-2 py-2 px-4 text-xs font-medium text-[#76787A] bg-white rounded-full shadow-[0px_1px_1px_0px_#0000001A] hover:bg-gray-100 hover:text-blue-700">
        <TbFilter size={16}/>
          Filters
        </button>
      </div>

      {/* Stats */}
      <div className="flex flex-col sm:flex-row items-center bg-white rounded p-2 text-center text-sm mb-6 shadow-[0px_2px_15px_0px_#1D24610D] gap-4 sm:gap-0">
        <div className="bg-cstm-grey px-6 py-4 rounded text-purple font-semibold w-full sm:w-auto text-center">
          <div className="text-[14px] font-normal text-[#050013]">Today</div>
          <div className="text-[16px] font-semibold">24 Jan</div>
        </div>
        <div className="flex-1 px-6 py-4">
          <div className="text-[16px] font-semibold text-[#050013]">02</div>
          <div className="text-[12px] font-normal text-[#76787A]">Rides</div>
        </div>
        <div className=" border-t sm:border-l sm:border-t-0 h-6"></div>
        <div className="flex-1 px-6 py-4">
          <div className="text-[16px] font-semibold text-[#050013]">08</div>
          <div className="text-[12px] font-normal text-[#76787A]">Passengers</div>
        </div>
      </div>

      {/* Trip List */}
      {scheduleData.map((trip) => (
        <div key={trip.id} className="flex items-start space-x-4 mb-4">
          <div className="w-20 text-right pt-1 text-sm text-[#76787A] hidden sm:block">{trip.time}</div>
          <div onClick={() => setSelectedTrip(trip.id)}
            className={`flex-1 cursor-pointer rounded-xl border bg-white px-4 sm:px-6 py-4 transition ${selectedTrip === trip.id ? 'border-blue-600 bg-blue-50' : 'border-none hover:border-gray-300'}`}>
            <div className="flex justify-between items-center mb-4">
              <div>
                <h3 className="text-[14px] font-medium text-[#050013] mb-2">{trip.route}</h3>
                <p className="text-[14px] text-purple font-normal">{trip.routeId}</p>
              </div>
              <div className="h-8 w-8 flex items-center justify-center rounded-full bg-cstm-grey">
                <IoIosArrowForward className="text-gray-800 w-4 h-4" />
              </div>
            </div>
            <div className="flex justify-between items-center flex-wrap gap-4">
              <div>
                <p className="text-[13px] text-[#76787A]">Total Stops</p>
                <p className="text-[16px] font-semibold text-[#050013]">{trip.totalStops}</p>
              </div>
              <div>
                <p className="text-[13px] text-[#76787A]">Passengers</p>
                <p className="text-[16px] font-semibold text-[#050013]">{trip.passengers}</p>
              </div>
              <div className="flex items-center">
                <TbWheelchair className="text-[#76787A] pr-2" size={28}/>
                <button className={`px-4 py-2 rounded-full text-[13px] font-medium transition ${selectedTrip === trip.id ? 'bg-cstm-blue-700 text-white' : 'border border-btn text-purple hover:bg-blue-50'}`}>
                  View Details
                </button>
              </div>
            </div>
          </div>
        </div>
      ))}
    </div>

    {/* Detail Panel */}
    <div className="w-full bg-white">
      {selected && (
        <div className="space-y-6">
          {/* Top Section */}
          <div className="flex justify-between items-start px-6 pt-6">
            <div className="flex">
              {stepSlide === 1 && (
                <div className="text-[#76787A] cursor-pointer mr-2 pt-1">
                  <SlArrowLeft onClick={() => setStepSlide(0)} />
                </div>
              )}
              <div>
                <h2 className="text-[16px] font-medium mb-1 text-[#050013]">{selected.route}</h2>
                <p className="text-purple font-normal text-[14px]">{selected.routeId}</p>
              </div>
            </div>
            <div className="flex space-x-3">
              <button onClick={() => setStepSlide(1)} className={`w-10 h-10 rounded-full border flex items-center justify-center text-xl ${stepSlide === 1 ? 'text-white bg-cstm-blue-700' : 'text-purple border-gray-300'}`}>
                <TbMap2 />
              </button>
              <button className="w-10 h-10 rounded-full border border-gray-300 flex items-center justify-center text-purple text-xl">
                <TbHelpTriangle />
              </button>
            </div>
          </div>

          {/* Meta Info */}
          <div className="flex flex-wrap gap-x-4 gap-y-2 text-[13px] text-gray-700 border-b px-6 pb-6">
            <div className="text-[#76787A]">Date: <span className="text-[#050013] font-medium">{selected.date} {selected.time}</span></div>
            <div className="text-[#76787A] border-l pl-2 h-4">Stops: <span className="text-[#050013] font-medium">{selected.totalStops}</span></div>
            <div className="text-[#76787A] border-l h-4 pl-2 flex items-center gap-2">Passengers:
              <span className="inline-flex items-center justify-center w-6 h-6 bg-gradient-to-br from-[#18EC95] to-[#3324E3] text-white text-xs rounded-full">{selected.passengers}</span>
            </div>
          </div>



<div className="overflow-y-auto custom-scrollbar px-6 h-[550px]">
          {/* Vehicle Details */}
          <div className="bg-gray-50 rounded-xl p-6 space-y-4">
            <h3 className="text-[13px] bg-white font-medium text-[#76787A] py-3 px-5 rounded-full">Vehicle Details</h3>
            <div className="grid grid-cols-2 sm:grid-cols-3 gap-4 text-sm text-gray-700 px-6">
              <div><span className="text-[#76787A]">License Plate:</span><br /><span className="font-medium text-[#050013]">"License"</span></div>
              <div><span className="text-[#76787A]">Model:</span><br /><span className="font-medium text-[#050013]">model</span></div>
              <div><span className="text-[#76787A]">Type:</span><br /><span className="font-medium text-[#050013]">type</span></div>
            </div>
          </div>

          {/* Passenger Details */}
          <div className="bg-gray-50 rounded-xl px-6 pb-6">
            <h3 className="text-[13px] bg-white font-medium text-[#76787A] py-3 px-5 rounded-full">Passenger Details</h3>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-y-3 text-sm text-gray-800 mt-5">
              {selected.passengerDetails.flat().map((name, i) => (
                <div key={i} className="flex items-center justify-around">
                  <span className="text-[#050013]">{name}</span>
                  <div className="flex space-x-2">
                    <LuPhone className="text-[#76787A] cursor-pointer hover:text-purple" />
                    <LuMessageSquareMore className="text-[#76787A] cursor-pointer hover:text-purple" />
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Stepper */}
          <div className="space-y-10 mt-6">
            {selected.steps.map((step, index) => (
              <div key={index} className="flex gap-4 relative">
                <div className="w-[80px] text-right text-xs text-gray-500 pt-1 hidden sm:block">
                  <div className="text-[#76787A] text-[13px]">{step.time}</div>
                  <div className="text-[#050013] text-[12px]">{step.type || "Depart"}</div>
                </div>
                <div className="relative flex flex-col items-center min-h-[60px]">
                  <div className={`w-4 h-4 rounded-full border-4 bg-white ${step.dot}`} />
                  {index !== selected.steps.length - 1 && (
                    <div className="absolute top-4 left-1/2 transform -translate-x-1/2 w-px h-[115px] border-l border-dashed border-gray-300" />
                  )}
                </div>
                <div className="flex-1">
                  <div className={`text-xs px-3 py-1.5 inline-block rounded-full font-medium mb-1 ${step.color}`}>
                    {step.label}
                  </div>
                  <div className="font-medium text-[#050013] text-[14px] mt-2 flex items-center gap-4">
                    {step.name}
                    {step.passengers !== undefined && (
                      <span className="flex items-center gap-1 text-[13px] text-[#050013] font-normal">
                        <LuUser className="text-xs" /> {step.passengers}
                      </span>
                    )}
                    {step.wheelchairs !== undefined && (
                      <span className="flex items-center gap-1 text-[13px] text-[#050013] font-normal">
                        <TbWheelchair className="text-xs" /> {step.wheelchairs}
                      </span>
                    )}
                  </div>
                  {step.address && (
                    <div className="text-[12px] text-[#030015] flex items-center gap-1 mt-1">
                      <span className="bg-blue-50 p-[6px] rounded-full text-[14px] mr-[5px]">
                        <GrLocation className="text-purple" />
                      </span>
                      {step.address}
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>

</div>



        </div>
      )}
    </div>

    {/* Map Panel */}
    <div className="w-full h-full bg-white flex items-center justify-center p-4">
      <img src="/images/map.jpg" alt="Map" className="object-cover w-full h-full rounded-xl" />
    </div>
  </div>
</div>
  </div>
</TabPanel>


            <TabPanel>
  <div className="border border-gray-300 rounded-xl">
    {/* Filter Bar */}
    <div className="bg-white pt-4 rounded-xl">
      {/* Top Row */}
      <div className="flex flex-wrap justify-between items-center gap-4 px-4 mb-4">
        {/* Left Section: Heading + Week Dropdown + Date Picker */}
        <div className="flex flex-wrap items-center gap-3">
          <h2 className="text-[16px] font-normal text-[#050013]">Shift</h2>

          {/* Week Dropdown */}
          <select className="border border-gray-300 text-sm rounded-full px-5 py-1.5 text-gray-700 bg-white focus:outline-none">
            <option>Week</option>
            <option>Month</option>
            <option>Day</option>
          </select>

          {/* Date Picker */}
          <div className="flex items-center border border-gray-300 rounded-full px-4 py-2 text-sm text-gray-700 bg-white">
            <span className="mr-2">Jan 20 - Jan 27</span>
            <span className="text-gray-400">2025</span>
          </div>
        </div>

        {/* Right Section: Night/Day Shift Buttons + Add */}
        <div className="flex flex-wrap items-center gap-3">
          <button className="bg-[#F1EDFF] text-[#050013] px-4 py-2 rounded-full text-sm font-normal flex items-center gap-2">
            Night Shift <TbMoonStars className="text-[16px] text-btn"/>
          </button>
          <button className="bg-[#FFF7E6] text-[#050013] px-4 py-2 rounded-full text-sm font-normal flex items-center gap-2">
            Day Shift <TbSunHigh className="text-[16px] text-[#FFB200]"/>
          </button>
    


<button
  className="px-3 py-2 flex items-center rounded-full text-sm font-medium transition bg-cstm-blue-700 text-white gap-2"
  onClick={() => setAvailabilityModal(true)}
>
  <FaPlus className="text-sm" />
  <span className="sm:hidden">Add</span>              {/* Small screen */}
  <span className="hidden sm:inline">Add Availability</span> {/* Large screen */}
</button>

        </div>
      </div>

      {/* Second Row: Search + Filter/Refresh Buttons */}
   {/* Search + Buttons Area */}
<div className="flex flex-wrap sm:flex-nowrap items-center justify-between gap-3 p-3 bg-tables">
  {/* Search Bar */}
  <form className="flex-1">
    <label className="sr-only">Search</label>
    <div className="relative">
      <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
        <IoIosSearch className="w-[20px] h-[20px] text-[#050013]" />
      </div>
      <input
        type="search"
        className="w-full sm:w-[300px] p-2 pl-10 text-sm text-gray-900 border border-gray-300 rounded-full focus:ring-blue-500 focus:border-blue-500 shadow-[0_10px_40px_0_#0000000D]"
        placeholder="Search here"
        onChange={(e) => setSearchTerm(e.target.value)}
      />
    </div>
  </form>

  {/* Filter + Refresh Buttons */}
  <div className="flex items-center gap-2">
    {/* Filter Button */}
    <button
      type="button"
      className="flex items-center gap-2 py-2 px-4 text-xs font-medium text-[#76787A] bg-white rounded-full shadow-[0px_1px_1px_0px_#0000001A] hover:bg-gray-100 hover:text-blue-700"
    >
      {/* Icon */}
      <TbFilter size={16}/>

      {/* Label */}
      <span className="hidden sm:inline">Filters</span>
    </button>

    {/* Refresh Button */}
    <button
      type="button"
      aria-label="refresh"
      className="flex items-center justify-center p-2 text-[#76787A] bg-white rounded-full border border-gray-200 hover:bg-gray-100 hover:text-blue-700"
    >
      <PiClockCounterClockwiseLight size={20} />
     
    </button>
  </div>
</div>

    </div>

    {/* Static Calendar */}
    <div className="overflow-x-auto rounded-xl shadow-sm">
      <div className="grid grid-cols-8 min-w-[900px]">
        <div className="bg-[#FAFAFA] border-r"></div>
        {days.map((day) => (
          <div key={day} className="bg-[#FAFAFA] border-b text-center text-sm font-semibold text-[#76787A] py-3 border-r">{day}</div>
        ))}
        {times.map((time) => (
          <React.Fragment key={time}>
            <div className="text-xs text-[#76787A] text-right pr-2 pt-4 border-b border-r bg-[#FAFAFA]">{time}</div>
            {days.map((day) => (
              <div key={day + time} className="border-b border-r min-h-[60px] p-1">
                {(day === "MON" && time === "6 AM") && (
                  <div className="bg-[#F3F0FF] text-[#6F3FF5] px-3 py-1 rounded-md text-xs font-medium"><TbMoonStars /> Night Shift</div>
                )}
                {(day === "THU" && time === "6 AM") && (
                  <div className="bg-[#FFF0F0] text-[#D12953] px-3 py-1 rounded-md text-xs font-medium">On Leave</div>
                )}
                {(day === "SAT" && time === "8 AM") && (
                  <div className="bg-[#FFF3D1] text-[#A96B00] px-3 py-1 rounded-md text-xs font-medium">Day Shift ☀️</div>
                )}
              </div>
            ))}
          </React.Fragment>
        ))}
      </div>
    </div>
  </div>
</TabPanel>

          </TabPanels>
        </TabGroup>
      </div>
    {availabilityModal &&
      <div className="fixed inset-0 z-[999] flex justify-end bg-black/30 transition-opacity duration-300 ease-in-out">
          <div className="w-full sm:max-w-md bg-white absolute bottom-0 h-[500px] overflow-y-scroll sm:relative sm:h-full 
          sm:overflow-visible shadow-lg transform transition-all duration-500 translate-x-0 sm:translate-x-0 rounded-l-2xl 
          flex flex-col justify-between">
            <div>
              <div className="flex justify-between items-center mb-6 p-6 bg-cstm-grey rounded-tl-[18px]">
                <h3 className="text-lg font-normal text-[#050013]">Add Availability</h3>
                <button
                  onClick={() => setAvailabilityModal(false)}
                  className="text-gray-400 hover:text-black text-xl"
                >
                  <FiX />
                </button>
              </div>

              <div className="space-y-4 p-6">
             
              <div className="space-y-5 text-sm text-[#111827] font-medium">
      {/* Select Shift */}
      <select className="w-full border rounded-lg px-4 py-3 text-[13px] font-normal text-[#76787A] focus:outline-none focus:ring-2 focus:ring-[#6F3FF5]">
        <option>Select Shift</option>
        <option>Morning</option>
        <option>Evening</option>
      </select>

      {/* Date Range Picker */}
      <div className="relative">
        <DatePicker                                                    
        className="w-full px-4 py-3 border font-normal rounded-lg text-[#050013] placeholder-[#76787A] text-[13px]"
        showYearDropdown
        showMonthDropdown
        scrollableYearDropdown
        dateFormat="dd/MM/yyyy"
        placeholderText="Select Date"
        minDate={new Date()}
     />
        <span className="absolute right-4 top-[10px] text-[#76787A] text-[20px]">
        <PiCalendarDot className="text-[#76787A]"/>
        </span>
      </div>

      {/* Start & End Time */}
      <div className="flex gap-4">
        <div className="relative w-full">
          <input
            type="text"
            placeholder="Start time"
            className="w-full border rounded-lg px-4 py-3 text-[13px] text-[#050013] font-normal placeholder-[#76787A] focus:outline-none"
          />
          <span className="absolute right-4 top-[10px] text-[#76787A] text-[20px]">
          <CiClock2 />

          </span>
        </div>
        <div className="relative w-full">
          <input
            type="text"
            placeholder="End time"
            className="w-full border rounded-lg px-4 py-3 text-[13px] font-normal text-[#050013] placeholder-[#76787A] focus:outline-none"
          />
          <span className="absolute right-4 top-[10px] text-[#76787A] text-[20px]">
          <CiClock2 />
          </span>
        </div>
      </div>

      {/* Add Leave Section */}
      <div className="space-y-3 pt-2">
        <p className="text-[#050013] font-medium text-[14px]">Add leave</p>

        <div className="relative">
          {/* <input
            type="text"
            placeholder="Select date"
            className="w-full border rounded-lg px-4 py-3 text-sm text-[#050013] placeholder-[#76787A] font-normal focus:outline-none"
          /> */}
            <DatePicker                                                    
        className="w-full px-4 py-3 border font-normal rounded-lg text-[#050013] placeholder-[#76787A] text-[13px]"
        showYearDropdown
        showMonthDropdown
        scrollableYearDropdown
        dateFormat="dd/MM/yyyy"
        placeholderText="Select Date"
        minDate={new Date()}
     />
          <span className="absolute right-4 top-[10px] text-[#76787A] text-[20px]">
          <PiCalendarDot className="text-[#76787A]"/>
          </span>
        </div>

        <textarea
          placeholder="Description"
          className="w-full border rounded-lg px-4 py-3 text-sm text-[#050013] placeholder-[#76787A] font-normal resize-none focus:outline-none"
          rows={2}
        ></textarea>
  </div>
    </div>
             
             
              </div>
            </div>

            <div className="p-6 flex justify-end border-t">
              <button className="px-6 py-2 flex items-center rounded-full text-sm font-medium transition bg-cstm-blue-700 text-white gap-5">
                Save
              </button>
            </div>
          </div>
      </div>
    }


{/* automatic modal start  */}
{rideRequestModal && (
  <div className="fixed inset-0 flex items-center justify-center bg-black/30 z-[1000]">
    <div className="bg-white w-[90%] max-w-sm rounded-2xl shadow-xl relative">
      {/* Close Button */}
      <button
        onClick={() => setRideRequestModal(false)}
        className="absolute -top-[7%] right-0 text-gray-400 hover:text-gray-700 text-lg"
      >
        <FiX />
      </button>

      {/* Heading */}
      <h2 className="text-[15px] font-normal text-[#050013] px-6 pt-6">New Ride Request</h2>

      {/* Price + Countdown Circle */}
      <div className="flex items-center justify-between mb-6 px-6 pb-3 border-b">
        <div className="text-[24px] font-medium text-[#050013]">€ 35.00</div>
        <div className="relative w-14 h-14 flex items-center justify-center">

  <div className="absolute w-full h-full rounded-full border-[3px] border-[#D4FFE6]">
  </div>

 
  <div className="absolute w-full h-full rounded-full border-[3px] 
              border-t-[#24DA96] border-r-transparent border-b-transparent border-l-transparent 
              animate-spin-slow"></div>

 
  <span className="text-xs text-[#050013] font-semibold z-10">05:10</span>
</div>

      </div>

      {/* Address and Details */}
      <div className="space-y-4 text-sm px-6 pb-6">
        <div className="flex items-start gap-3">
        <div className="flex flex-col items-center">
  {/* First Icon */}
  <div className="rounded-full p-1 bg-[#F0EFFF] text-purple">
    <TbMapPin />
  </div>

  {/* Dashed Line Centered */}
  <div className="h-5 border-l border-dashed border-gray-400 my-1 self-center"></div>

  {/* Second Icon */}
  <div className="rounded-full p-1 bg-[#F0EFFF] text-purple">
    <LuFlagTriangleRight />
  </div>
</div>

          <div className="flex-1 space-y-6">
            <div className="flex justify-between">
              <p className="text-[#050013] font-normal text-[12px]">288-292, Spuistraat City</p>
              <p className="text-[#76787A] text-[12px] font-normal">1 Hr. 15 min</p>
              <p className="text-[#050013] text-[12px] font-normal">18km</p>
            </div>
            <div className="flex justify-between">
              <p className="text-[#050013] font-normal text-[12px]">1000 AP, Schipol Airport</p>
              <p className="text-[#76787A] text-[12px] font-normal">2 Hr. 15 min</p>
              <p className="text-[#050013] text-[12px] font-normal">45km</p>
            </div>
          </div>
        </div>
      </div>

      {/* Accept Button */}
      <div className="px-6 pb-6 pt-3 border-t">

      <div className="flex justify-between items-center mt-2 px-1">
          <p className="text-[#050013] text-[12px] font-normal">This ride has 5 Stops</p>
          <div className="flex items-center text-[#18EC95] font-medium text-[12px] gap-2">
          <span className="border border-green p-1 rounded-full">
          <LuEuro/>
          </span>
          Pre Paid
          </div>
        </div>
        <Link href="/driver/start-ride">
        <button className="mt-4 w-full py-3 bg-[#3707EF] text-white text-[14px] rounded-full text-base font-semibold shadow">
        Accept
      </button>
      </Link>
      </div>
    </div>
  </div>
)}


{/* automatic modal end  */}


    </div>
  );
}