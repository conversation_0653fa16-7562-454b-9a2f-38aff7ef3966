"use client";

import { useEffect, useState } from "react";
import { useHeaderTitle } from "@/context/HeaderTitleContext";
import { TbPhoneCall } from "react-icons/tb";
import { FiChevronDown, FiX } from "react-icons/fi";
import { PiMapPinLight } from "react-icons/pi";
import { RxStarFilled } from "react-icons/rx";
import { LuFlagTriangleRight } from "react-icons/lu";
import { GoDownload } from "react-icons/go";



export default function StartRidePage() {
  const { setTitle } = useHeaderTitle();
  const [isRightPanel, setIsRightPanel] = useState<boolean>(false);
  const [isRoutePanel, setIsRoutePanel] = useState<boolean>(false);
  useEffect(() => {
    setTitle("Start Ride");
  }, []);

  return (
    <div className="max-w-7xl mx-auto sm:p-4 ">
      <div className="bg-white rounded-2xl border flex flex-col md:flex-row overflow-hidden">
        {/* Left Panel */}
        <div className="hidden sm:block w-full md:w-1/2 space-y-6 relative">
          {/* Rider Info */}
          <div className="space-y-4 p-6">
            {/* Name and Rating */}
            <div className="flex items-start gap-3">
              <img
                src="/images/avatar.jpg"
                alt="Rider"
                className="w-12 h-12 rounded-full object-cover"
              />
              <div>
                <div className="flex items-center gap-2">
                  <p className="font-medium text-[#050013] text-[16px]">Rivka Frank</p>
                  <div className="flex items-center text-[15px] text-yellow-500">
                  <RxStarFilled />
                    <span className="ml-1 text-[15px] text-[#050013] font-normal">4.5</span>
                  </div>
                </div>


                <div className="relative w-40">
                <select className="appearance-none border-0 rounded-lg text-[12px] text-[#76787A] focus:outline-none">
                  <option>Fare Estimate</option>
                  <option>Premium</option>
                </select>
                <span className="absolute top-[14px] right-[30%] p-[2px] bg-tables rounded-[12px] -translate-y-1/2 pointer-events-none">
                  <FiChevronDown className="text-[#050013]" />
                </span>
              </div>

              <div>
              <p className="text-[20px] font-medium text-[#050013]">
                € 35.00
              </p>
            </div>
              </div>
            </div>
          </div>

          {/* Stops */}
          <div className="border-t p-6">
          <div className="relative">
            <p className="text-[13px] font-normal text-[#76787A] mb-4">This ride has 5 Stops</p>
            <div className="space-y-0">
              {/* Stop 1 */}
              <div className="flex items-start gap-3 relative">
                <div className="flex flex-col items-center">
                  <PiMapPinLight className="text-[#6F3FF5] p-[4px] rounded-full bg-blue-50 z-10" size={25} />
                  <div className="h-6 border-l-2 border-dashed border-gray-300" />
                </div>
                <p className="text-[13px] font-normal text-[#050013]">288–292, Spuistraat City</p>
              </div>
              {/* Stop 2 */}
              <div className="flex items-start gap-3 relative">
                <div className="flex flex-col items-center">
                  <LuFlagTriangleRight className="text-[#6F3FF5] p-[4px] rounded-full bg-blue-50 z-10" size={25} />
                </div>
                <p className="text-[13px] font-normal text-[#050013]">1000 AP, Schipol Airport...</p>
              </div>
            </div>
            {/* ETA Box inside Stops RHS */}
            <div className="absolute top-0 right-0 bg-tables px-4 py-2 rounded-lg text-center">
              <p className="text-[13px] font-normal text-[#76787A]">ETA</p>
              <p className="text-[16px] font-medium text-[#050013]">18 min</p>
            </div>
          </div>
          </div>
          {/* Start Ride CTA */}
          <div className="p-6">
          <button onClick={() => setIsRightPanel(true)}
          className="w-full py-3 bg-active-status hover:bg-[#00b24c] text-white font-medium text-sm rounded-full transition-all" style={{ backgroundColor: "#FF5F5F" }}>
          End route 
          </button>
          </div>
        </div>

        {/* Right Panel */}
        <div className="w-full md:w-1/2 relative bg-gray-100 flex flex-col items-center justify-start">
          {/* Map Image */}
          <img
            src="/images/map.png"
            alt="Route Map"
            className="w-full object-cover"
          />
        </div>
      </div>
      {/* Floating Phone Icon Button */}
      <div className="fixed bottom-6 right-6 z-50">
        <button className="bg-black hover:bg-gray-800 text-white p-4 rounded-full shadow-lg">
          <TbPhoneCall className="w-5 h-5" />
        </button>
      </div>

      {isRightPanel &&
        <div className="fixed inset-0 z-[999] flex justify-end bg-black/30 transition-opacity duration-300 ease-in-out">
        <div className="w-full sm:max-w-md bg-white h-full shadow-lg transform transition-all duration-500 translate-x-0 sm:translate-x-0 rounded-l-2xl flex flex-col justify-between">
          {/* Header */}
          <div>
            <div className="flex justify-between items-center mb-6 p-6 bg-tables rounded-tl-[18px]">
              <div>
                <h3 className="text-[20px] font-normal text-[#050013]">Ride Fare Details</h3>
                <p className="text-[12px] text-[#76787A] mt-1">Today at 2:45 PM</p>
              </div>
              <div className="flex items-center gap-3">
                <button className="p-2 text-purple border border-btn rounded-full">
                  <GoDownload className="w-4 h-4" />
                </button>
                <button
                  onClick={() => setIsRightPanel(false)}
                  className="text-gray-400 hover:text-black text-xl"
                >
                  <FiX />
                </button>
              </div>
            </div>
  
            {/* Route & Fare Box */}
            <div className="bg-[#F3F5F7] p-4 mx-6 rounded mb-6">
  <div className="space-y-4">
    {/* Start Point */}
    <div className="flex justify-between items-start">
      <div className="flex gap-3 items-start">
        <div className="flex flex-col items-center mb-0">
          <PiMapPinLight className="text-purple bg-white p-[4px] rounded-full" size={24} />
          {/* Dashed Line */}
          <div className="h-6 border-l-2 border-dashed border-gray-300" />
        </div>
        <p className="text-[13px] text-[#050013] font-normal">288–292, Spuistraat City</p>
      </div>
      <p className="text-[18px] text-[#050013] font-medium">€ 35.00</p>
    </div>

    {/* End Point */}
    <div className="flex gap-3 items-start">
      <div className="flex flex-col items-center">
        <LuFlagTriangleRight className="text-purple bg-white p-[4px] rounded-full" size={24} />
      </div>
      <p className="text-[13px] text-[#050013] font-normal">1000 AP, Schipol Airport...</p>
    </div>
  </div>
</div>

  
            {/* Details */}
            <div className="px-6 space-y-6">
              <div className="space-y-3 text-sm text-[#050013]">
                <div className="flex justify-between text-[#76787A] text-[14px]">
                  <span>Total distance traveled</span>
                  <span className="text-[#050013]">30 KM</span>
                </div>
                <div className="flex justify-between text-[#76787A]">
                  <span>Total time spent</span>
                  <span className="text-[#050013]">1:00 hr</span>
                </div>
                <div className="flex justify-between text-[#76787A]">
                  <span>Payment</span>
                  <span className="text-[#050013]">By card</span>
                </div>
              </div>
  
              <div>
                <p className="text-[16px] font-medium text-[#050013] mb-4">Fare Breakdown</p>
                <div className="space-y-3 text-sm text-[#050013] text-[14px]">
                  <div className="flex justify-between text-[#76787A]">
                    <span>Base Fare</span>
                    <span className="text-[#050013]">€ 25.00</span>
                  </div>
                  <div className="flex justify-between text-[#76787A]">
                    <span>Wait fees</span>
                    <span className="text-[#050013]">€ 5.00</span>
                  </div>
                  <div className="flex justify-between text-[#76787A]">
                    <span>Distance (1 KM)</span>
                    <span className="text-[#050013]">€ 1.00</span>
                  </div>
                  <div className="flex justify-between text-[#76787A]">
                    <span>Service fee</span>
                    <span className="text-[#050013]">€ 4.00</span>
                  </div>
                  <hr />
                  <div className="flex justify-between text-[#76787A]">
                    <span className="text-[13px]">Total Earning</span>
                    <span className="text-[#050013] font-semibold text-[16px]">€ 35.00</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
  
          {/* Bottom CTA */}
          <div className="p-6 border-t flex justify-end">
            <button className="py-3 px-4 rounded-full border border-btn text-purple text-[13px] font-semibold hover:bg-[#f2efff]">
              Report an issue
            </button>
          </div>
        </div>
      </div>
      }
    {/* end route modal start  */}
    
    <div className="sm:hidden fixed inset-0 flex justify-end bg-black/30 transition-opacity duration-300 ease-in-out">
        <div className="w-full sm:max-w-md bg-white shadow-lg fixed bottom-0 h-[50%] w-full bg-white rounded-tl-[15px] rounded-tr-[15px]">          
        <div className="w-full md:w-1/2 space-y-6 relative">
          {/* Rider Info */}
          <div className="space-y-4 p-6 mb-0 pb-4">
            {/* Name and Rating */}
            <div className="flex items-start gap-3">
              <img
                src="/images/avatar.jpg"
                alt="Rider"
                className="w-12 h-12 rounded-full object-cover"
              />
              <div>
                <div className="flex items-center gap-2">
                  <p className="font-medium text-[#050013] text-[16px]">Rivka Frank</p>
                  <div className="flex items-center text-[15px] text-yellow-500">
                  <RxStarFilled />
                    <span className="ml-1 text-[15px] text-[#050013] font-normal">4.5</span>
                  </div>
                </div>


                <div className="relative w-40">
                <select className="appearance-none border-0 rounded-lg text-[12px] text-[#76787A] focus:outline-none">
                  <option>Fare Estimate</option>
                  <option>Premium</option>
                </select>
                <span className="absolute top-[14px] right-[30%] p-[2px] bg-tables rounded-[12px] -translate-y-1/2 pointer-events-none">
                  <FiChevronDown className="text-[#050013]" />
                </span>
              </div>

              <div>
              <p className="text-[20px] font-medium text-[#050013]">
                € 35.00
              </p>
            </div>
              </div>
            </div>
          </div>

          {/* Stops */}
          <div className="border-t p-6 pb-0 mb-0">
          <div className="relative">
            <p className="text-[13px] font-normal text-[#76787A] mb-4">This ride has 5 Stops</p>
            <div className="space-y-0">
              {/* Stop 1 */}
              <div className="flex items-start gap-3 relative">
                <div className="flex flex-col items-center">
                  <PiMapPinLight className="text-[#6F3FF5] p-[4px] rounded-full bg-blue-50 z-10" size={25} />
                  <div className="h-6 border-l-2 border-dashed border-gray-300" />
                </div>
                <p className="text-[13px] font-normal text-[#050013]">288–292, Spuistraat City</p>
              </div>
              {/* Stop 2 */}
              <div className="flex items-start gap-3 relative">
                <div className="flex flex-col items-center">
                  <LuFlagTriangleRight className="text-[#6F3FF5] p-[4px] rounded-full bg-blue-50 z-10" size={25} />
                </div>
                <p className="text-[13px] font-normal text-[#050013]">1000 AP, Schipol Airport...</p>
              </div>
            </div>
            {/* ETA Box inside Stops RHS */}
            <div className="absolute top-0 right-0 bg-tables px-4 py-2 rounded-lg text-center">
              <p className="text-[13px] font-normal text-[#76787A]">ETA</p>
              <p className="text-[16px] font-medium text-[#050013]">18 min</p>
            </div>
          </div>
          </div>
          {/* Start Ride CTA */}
          <div className="p-6">
          <button onClick={() => setIsRightPanel(true)}
          className="w-full py-3 bg-active-status hover:bg-[#00b24c] text-white font-medium text-sm rounded-full z-999 transition-all" style={{ backgroundColor: "#FF5F5F" }}>
          End route 
          </button>
          </div>
        </div>
        </div>
    </div>
      
    {/* end route modal end  */}
    </div>
  );
}