"use client"
import React, { useState } from 'react';
import { MapPin, Calendar, Clock, Car, Zap, Truck, VolumeX, Heart, CreditCard, Banknote, User } from 'lucide-react';
import { LuMapPin, LuFlagTriangleRight } from "react-icons/lu";
import { RiCalendarEventLine } from "react-icons/ri";
import { FiCheck, FiChevronDown, FiCreditCard } from "react-icons/fi";
import { TbWheelchair } from "react-icons/tb";
import { FaRegCreditCard } from "react-icons/fa6";
import { BlueCar, GreenCar, Ev } from '@/icons';
import Image from "next/image";


export default function RideBookingApp() {
  const [selectedRideType, setSelectedRideType] = useState('EV');
  const [selectedPayment, setSelectedPayment] = useState('Card');
  const [preferences, setPreferences] = useState({
    wheelchair: false,
    quietRide: false,
    petFriendly: false
  });
  const [automatedReminder, setAutomatedReminder] = useState(true);

  const rideTypes = [
    { id: 'EV', name: 'EV', description: 'Comfy, economical cars', icon: Ev, color: 'bg-green-100 text-green-600' },
    { id: 'Sedan', name: 'Sedan', description: 'Comfy, economical cars', icon: BlueCar, color: 'bg-blue-100 text-blue-600' },
    { id: 'SUV', name: 'Suv', description: 'Comfy, economical cars', icon: GreenCar, color: 'bg-gray-100 text-gray-600' }
  ];
   
  const preferenceOptions = [
    { id: 'wheelchair', label: 'Wheelchair', icon: TbWheelchair },
    { id: 'quietRide', label: 'Quiet Ride', icon: TbWheelchair },
    { id: 'petFriendly', label: 'Pet-Friendly Ride', icon: TbWheelchair }
  ];

  const togglePreference = (pref) => {
    setPreferences(prev => ({
      ...prev,
      [pref]: !prev[pref]
    }));
  };

const [paymentMethod, setPaymentMethod] = useState("Card");
const fareOptions = ["Fare Estimate", "Fixed Fare", "Metered Fare"];
  return (
    <div className='mt-[-15px] sm:mt-4 pb-6 sm:pb-0'>
      <div className="flex flex-col sm:flex-row h-screen bg-white">
        {/* Map Section */}
        <div className="w-full sm:flex-1 relative h-[300px] sm:h-full">
          <div className="absolute inset-0 bg-gradient-to-br from-blue-100 to-green-100 rounded-t-2xl sm:rounded-t-2xl">
            {/* Map placeholder with locations */}
            <div className="relative h-full w-full overflow-hidden sm:rounded-t-2xl">
              {/* Map background pattern */}
              <div className="absolute inset-0 opacity-20">
                <svg className="w-full h-full" viewBox="0 0 400 600">
                  <defs>
                    <pattern id="mapPattern" x="0" y="0" width="40" height="40" patternUnits="userSpaceOnUse">
                      <rect width="40" height="40" fill="#f0f9ff" />
                      <path d="M0 40L40 0M-10 10L10 -10M30 50L50 30" stroke="#dbeafe" strokeWidth="1" />
                    </pattern>
                  </defs>
                  <rect width="100%" height="100%" fill="url(#mapPattern)" />
                </svg>
              </div>

              {/* Location markers */}
              <div className="absolute top-16 left-16 bg-white rounded-full p-2 shadow-lg">
                <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                <div className="absolute -top-2 -right-2 bg-blue-500 text-white text-xs px-1 rounded">
                  De Merenhof - Ons Tweede Thuis
                </div>
              </div>

              <div className="absolute top-32 right-24 bg-white rounded-full p-2 shadow-lg">
                <div className="w-3 h-3 bg-gray-400 rounded-full"></div>
                <div className="absolute -top-2 -left-8 bg-gray-500 text-white text-xs px-1 rounded">
                  Rijschool Ton de Haan
                </div>
              </div>

              <div className="absolute bottom-40 left-20 bg-white rounded-full p-2 shadow-lg">
                <div className="w-3 h-3 bg-gray-400 rounded-full"></div>
                <div className="absolute -top-2 -right-4 bg-gray-500 text-white text-xs px-1 rounded">
                  Plusschool
                </div>
              </div>

              <div className="absolute bottom-32 right-16 bg-white rounded-full p-2 shadow-lg">
                <div className="w-3 h-3 bg-gray-400 rounded-full"></div>
                <div className="absolute -top-2 -left-6 bg-gray-500 text-white text-xs px-1 rounded">
                  S & S Catering
                </div>
              </div>

              <div className="absolute bottom-16 left-32 bg-white rounded-full p-2 shadow-lg">
                <div className="w-3 h-3 bg-gray-400 rounded-full"></div>
                <div className="absolute -top-2 -right-8 bg-gray-500 text-white text-xs px-1 rounded">
                  Jeugdtheaterschool Abcoude
                </div>
              </div>

              {/* Current location marker */}
              <div className="absolute top-1/2 left-1/3 transform -translate-x-1/2 -translate-y-1/2">
                <div className="w-6 h-6 bg-blue-600 rounded-full border-4 border-white shadow-lg relative">
                  <div className="absolute inset-0 bg-blue-600 rounded-full animate-ping opacity-75"></div>
                </div>
              </div>

              {/* Route line */}
              <svg className="absolute inset-0 w-full h-full">
                <path
                  d="M100 150 Q200 250 300 400"
                  stroke="#3b82f6"
                  strokeWidth="4"
                  fill="none"
                  strokeDasharray="10,5"
                  className="opacity-70"
                />
              </svg>

              {/* Destination markers */}
              <div className="absolute bottom-24 right-40 bg-blue-600 text-white rounded-full p-2 shadow-lg">
                <div className="w-4 h-4 bg-white rounded-full flex items-center justify-center">
                  <div className="w-2 h-2 bg-blue-600 rounded-full"></div>
                </div>
                <div className="absolute -top-2 -left-8 bg-blue-600 text-white text-xs px-2 py-1 rounded">
                  PLUS Koot
                </div>
              </div>

              <div className="absolute bottom-8 right-32 bg-blue-600 text-white rounded-full p-2 shadow-lg">
                <div className="w-4 h-4 bg-white rounded-full flex items-center justify-center">
                  <div className="w-2 h-2 bg-blue-600 rounded-full"></div>
                </div>
                <div className="absolute -top-2 -left-6 bg-blue-600 text-white text-xs px-2 py-1 rounded">
                  Albert Heijn
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Booking Panel */}
        <div className="mt-6 sm:mt-0 w-full sm:w-[50%] bg-white px-6 overflow-y-auto custom-scrollbar max-h-[calc(100vh-300px)] sm:max-h-full">
          <div className="space-y-6">
            {/* Location Inputs */}
            <div className="space-y-4">
              <div className="flex items-center space-x-3 py-3 px-5 border rounded-lg">
              <span className='bg-blue-100 text-purple p-[6px] rounded-full'><LuMapPin/></span>
                <input
                  type="text"
                  placeholder="Pickup location test"
                  className="flex-1 outline-none text-[#76787A]"
                />
              </div>

              <div className="flex items-center space-x-3 py-3 px-5 border rounded-lg">
              <span className='bg-blue-100 text-purple p-[6px] rounded-full'><LuFlagTriangleRight/></span>
                <input
                  type="text"
                  placeholder="Drop off location"
                  className="flex-1 outline-none text-[#76787A]"
                />
              </div>

              <button className="text-purple text-[15px] font-normal">+ Add Stop</button>
            </div>

            {/* Date & Time */}
            <div className="flex items-center space-x-3 py-3 px-5 border rounded-lg">
              <span className='bg-blue-100 text-purple p-[6px] rounded-full'><RiCalendarEventLine/></span>
              <span className="text-[#76787A]">Select date & time</span>
            </div>

            {/* Ride Type Selection */}
            <div>
                    <h3 className="text-[16px] font-medium mb-4">Select Ride Type</h3>
                     <div className="grid grid-cols-1 sm:grid-cols-1 md:grid-cols-3 gap-3">
                      {rideTypes.map((type) => (
                        <button key={type.id} onClick={() => setSelectedRideType(type.id)} 
                          className={`flex sm:flex-col align-center sm:align-left relative p-4 rounded-lg border transition-all ${selectedRideType === type.id
                           ? '' : 'border-gray-200 hover:border-gray-300'}`}>
                          {/* Top-right checkbox */}
                          <div className="absolute top-6 sm:top-2 right-2 w-5 h-5 rounded-md border border-gray-300 flex items-center justify-center bg-white">
                            {selectedRideType === type.id && (
                              <div className="w-5 h-5 bg-blue-600 rounded-sm">
                                {selectedRideType === type.id && (
                             <FiCheck className="text-white text-xs" size={18} />
                                )}
                              </div>
                               )}
                          </div>

                              {/* Icon circle */}
                              <div className={`w-12 h-12 rounded-full ${type.color} flex items-center justify-center sm:mb-4`}>
                                {/* <type.icon size={20} className="w-6 h-6" /> */}
                                <Image src={type.icon} alt="404" className="dark:hidden" width={472} height={152} />
                              </div>

                                {/* Texts */}
                                <div className='pl-5 sm:pl-0'>
                                <div className="text-[14px] font-medium text-left">{type.name}</div>
                                <div className="text-[14px] text-gray-500 mt-1 text-left">{type.description}</div>
                                </div>
                          </button>
                                   ))}
                    </div>
            </div>

            {/* Preferences */}
            <div>
              <h3 className="text-[16px] font-medium mb-4 text-[#050013]">Preferences</h3>
              <div className="flex flex-wrap gap-3">
                {preferenceOptions.map((option) => (
                  <button
                    key={option.id}
                    onClick={() => togglePreference(option.id)}
                    className={`flex items-center space-x-2 px-4 py-2 rounded-full border transition-all ${preferences[option.id]
                      ? 'bg-blue-50 border-blue-500 text-blue-700'
                      : 'border-gray-200 text-gray-600 hover:border-gray-300'
                      }`}
                  >
                    <option.icon size={20} className='text-[#76787A]'/>
                    <span className="text-[15px] text-[#050013]">{option.label}</span>
                  </button>
                ))}
              </div>
            </div>

            {/* Automated Reminder */}
            <div className="flex items-center justify-between">
              <span className="text-[16px] font-medium">Automated Reminder</span>
              <button
                onClick={() => setAutomatedReminder(!automatedReminder)}
                className={`relative w-12 h-6 rounded-full transition-colors ${automatedReminder ? 'bg-[#14EC95]' : 'bg-gray-300'
                  }`}
              >
                <div
                  className={`absolute top-0.5 w-5 h-5 bg-white rounded-full shadow-md transition-transform ${automatedReminder ? 'translate-x-6' : 'translate-x-0.5'
                    }`}
                />
              </button>
            </div>

            <div className="bg-tables rounded-2xl p-6 w-full">
        {/* Ride Estimation */}
        <div className="mb-6">
          <h2 className="text-[18px] font-medium text-[#050013] mb-3">Ride Estimation</h2>
          <div className="flex justify-between items-center mb-2">
          <div className="relative w-[50%] sm:w-[30%]">
            <select
              className="appearance-none text-[14px] text-[#76787A] pr-10 focus:outline-none focus:ring-2 focus:ring-[#3B00F3]"
              defaultValue="Fare Estimate"
            >
              {fareOptions.map((option, index) => (
                <option key={index} value={option}>
                  {option}
                </option>
              ))}
            </select>
            <span className="absolute rounded-full p-[2px] right-3 top-1/2 transform -translate-y-1/2 text-[#050013] pointer-events-none text-lg bg-gray-300">
              <FiChevronDown />
              </span>
          </div>
            <span className="text-[16px] font-medium text-[#050013]">€ 35.00</span>
          </div>
          <div className="flex justify-between items-center space-y-2">
            <span className="text-[14px] text-[#76787A]">Date & Time</span>
            <span className="text-[16px] font-medium text-[#050013]">15-02-2025 10:30AM</span>
          </div>
        </div>

        {/* Payment Method */}
        <div className="mb-6">
          <h2 className="text-[18px] font-medium text-[#050013] mb-3">Payment Method</h2>
          <div className="flex flex-col sm:flex-row gap-4">
    {["Card", "Cash"].map((method) => (
      <button
        key={method}
        onClick={() => setPaymentMethod(method)}
        className={`flex items-center justify-between px-4 py-3 rounded-xl w-full
          bg-white ${
            paymentMethod === method ? "shadow-[0px_2px_15px_0px_#1D24610D]" : "shadow-[0px_2px_15px_0px_#1D24610D]"
          } transition`}
      >
        {/* Left: Icon + Text */}
        <div className="flex items-center gap-2">
          <FaRegCreditCard
            className={`text-lg ${
              method === "Card" ? "text-[#76787A]" : "text-[#76787A]"
            }`}
          />
          <span className="text-sm font-medium text-[#050013]">{method}</span>
        </div>

        {/* Right: Tickbox with check icon if selected */}
        {paymentMethod === method && (
          <div className="w-5 h-5 rounded bg-[#3B00F3] flex items-center justify-center">
            <FiCheck className="text-white text-xs" />
          </div>
        )}
      </button>
    ))}
          </div>
        </div>

        {/* Book Ride Button */}
        <button className="w-full bg-[#3707EF] text-white font-medium py-3 rounded-full">
          Book Ride
        </button>
            </div>

          </div>
        </div>      
      </div>
    </div>
  );
}