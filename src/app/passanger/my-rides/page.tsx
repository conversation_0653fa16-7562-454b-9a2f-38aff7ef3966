"use client";

import React, { useEffe<PERSON>, useState } from "react";
import {
  <PERSON>b,
  TabGroup,
  TabList,
  TabPanel,
  TabPanels,
} from "@headlessui/react";
import { useHeaderTitle } from "@/context/HeaderTitleContext";

// FULL ICON + COMPONENT IMPORTS (as requested)
import { PiCalendarDot, PiClockCounterClockwiseLight,PiCalendarDotThin } from "react-icons/pi";
import { FiMic, FiCamera, FiX } from "react-icons/fi";
import { motion } from "framer-motion";
import { TfiCheckBox } from "react-icons/tfi";
import {
  IoIosArrowForward,
  IoIosSearch
} from "react-icons/io";
import {
  TbWheelchair,
  TbMap2,
  TbHelpTriangle,
  TbChevronDown,
  TbMoonStars,
  TbSunHigh,
  TbFilter,
  TbMapPin
} from "react-icons/tb";
import {
  <PERSON><PERSON><PERSON>,
  LuMessageSquareMore,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>lag<PERSON><PERSON>gleRight,
  <PERSON>E<PERSON>
} from "react-icons/lu";
import {
  FaPhone,
  FaCommentDots,
  FaUser,
  FaWheelchair,
  FaLocationArrow,
  FaPlus
} from "react-icons/fa";
import { GrLocation } from "react-icons/gr";
import { CiClock2 } from "react-icons/ci";
import { SlArrowLeft } from "react-icons/sl";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import Link from "next/link";

// Sample Data
const upcomingRides = [
  {
    id: "#RD45692",
    from: "Spuistraat City",
    to: "Schipol Airport",
    date: "Feb 15, 2025",
    time: "9:30 AM",
    price: "€32.50",
    type: "today",
  },
  {
    id: "#RD45693",
    from: "City Center",
    to: "Hotel Airport",
    date: "Feb 28, 2025",
    time: "12:30 PM",
    price: "€32.50",
    type: "later",
  },
  {
    id: "#RD45694",
    from: "City Center",
    to: "Hotel Airport",
    date: "Mar 1, 2025",
    time: "12:30 PM",
    price: "€32.50",
    type: "later",
  },
];

const completedRides = [
  {
    id: "#RD45500",
    from: "Hotel Airport",
    to: "Downtown",
    date: "Jan 10, 2025",
    time: "2:00 PM",
    price: "€28.00",
  },
];

// Ride Card Component
const RideCard = ({ id, from, to, date, time, price }) => (
  <div className="bg-white rounded-xl shadow-md px-6 py-5 flex justify-between items-start">
    <div className="space-y-1">
      <p className="text-sm text-[#5B5B5B] font-medium">{id}</p>
      <div className="flex items-center gap-2 text-sm text-black font-semibold">
        <span>{from}</span>
        <span className="text-gray-400">•••••</span>
        <span className="text-primary-500">{to}</span>
      </div>
      <p className="text-sm text-gray-500">
        {date} • {time}
      </p>
      <p className="font-semibold text-lg mt-1">{price}</p>
    </div>
    <button className="border border-gray-400 text-sm text-gray-700 px-4 py-1 rounded-full hover:bg-gray-100">
      Cancel
    </button>
  </div>
);

// Main Page
export default function MyRidesPage() {
  const { setTitle } = useHeaderTitle();

  useEffect(() => {
    if (setTitle) setTitle("Passenger Help");
  }, [setTitle]);

  const todayRides = upcomingRides.filter((r) => r.type === "today");
  const laterRides = upcomingRides.filter((r) => r.type === "later");
  const [showModal, setShowModal] = useState(false);
  const [isCanceled, setIsCanceled] = useState(false);
  return (
    <div className="p-6 mx-auto">
      <TabGroup>
           <TabList className="flex gap-6 px-6 py-4 bg-white">
             <Tab className={({ selected }) => `flex items-center space-x-2 pb-3 border-b-2 text-sm font-medium outline-none ${selected ? 'border-blue-600 text-purple' : 'border-transparent text-[#76787A]'}`}> <PiCalendarDot className="w-[20px] h-[20px]" /> <span>Upcoming</span> </Tab>
             <Tab className={({ selected }) => `flex items-center space-x-2 pb-3 border-b-2 text-sm font-medium outline-none ${selected ? 'border-blue-600 text-purple' : 'border-transparent text-[#76787A]'}`}> <TfiCheckBox className="w-[20px] h-[20px]" /> <span>Completed</span> </Tab>
           </TabList>

        <TabPanels className="mt-6 space-y-6">
          {/* Upcoming Tab Panel */}
          <TabPanel className="space-y-6">
  {/* Today Section */}
  {todayRides.length > 0 && (
    <div className="space-y-3">
      <p className="text-[#76787A] text-[14px] font-normal">Today</p>
      {todayRides.map((ride, idx) => (
  <div
  key={idx}
  className={`bg-white rounded-xl shadow-[0px_2px_15px_0px_#1D24610D] p-6 mb-6 flex flex-col sm:flex-row justify-between gap-4 items-center ${
    isCanceled ? "opacity-70" : ""
  }`}
>
    {/* Left: Ride details */}
    <div className="space-y-1 w-full">
      <p className="text-[16px] text-purple font-medium">{ride.id}</p>

      <div className="flex items-center gap-2 text-[16px] text-[#050013] font-medium truncate">
        <span>{ride.from}</span>
        <span className="p-1 bg-cstm-blue-700 rounded-full"></span>
        <span className="w-10 border-t border-dashed border-gray-400"></span>
        <span className="p-1 bg-cstm-blue-700 rounded-full"></span>
        <span className="truncate">{ride.to}</span>
      </div>

      <p className="text-[14px] text-[#76787A] font-normal">
        {ride.date} • {ride.time}
      </p>

      {/* Mobile View: Price + Cancel inline */}
      <div className="flex justify-between items-center pt-1 sm:hidden">
        <p className="font-semibold text-[18px]">{ride.price}</p>
        <button
        onClick={() => {
          if (!isCanceled) setShowModal(true); // Disable reopen if already canceled
        }}
        className={`text-sm px-4 py-1 rounded-full transition 
          ${isCanceled 
            ? "bg-red-100 text-red-600 border border-red-400 cursor-default" 
            : "border border-gray-400 text-gray-700 hover:bg-gray-100"}`}
      >
        {isCanceled ? "Canceled" : "Cancel"}
      </button>
      </div>

      {/* Desktop-only price */}
      <p className="font-semibold text-[#050013] text-[20px] hidden sm:block">{ride.price}</p>
    </div>

    {/* Desktop-only Cancel button */}
    <div className="hidden sm:flex items-start">
    <button
        onClick={() => {
          if (!isCanceled) setShowModal(true); // Disable reopen if already canceled
        }}
        className={`text-sm px-4 py-1 rounded-full transition 
          ${isCanceled 
            ? "text-red-600 border border-transparent cursor-default" 
            : "border btn-border text-[#76787A] hover:bg-gray-100"}`}
      >
        {isCanceled ? "Canceled" : "Cancel"}
      </button>
    </div>
  </div>
))}

    </div>
  )}


{todayRides.length > 0 && (
    <div className="space-y-3">
      {todayRides.map((ride, idx) => (
        <div
        key={idx}
        className={`bg-white rounded-xl shadow-[0px_2px_15px_0px_#1D24610D] p-6 mb-6 flex flex-col sm:flex-row justify-between gap-4 items-center ${
          isCanceled ? "opacity-50" : ""
        }`}
      >
    {/* Left: Ride details */}
    <div className="space-y-1 w-full">
      <p className="text-[16px] text-purple font-medium">{ride.id}</p>

      <div className="flex items-center gap-2 text-[16px] text-[#050013] font-medium truncate">
        <span>{ride.from}</span>
        <span className="p-1 bg-cstm-blue-700 rounded-full"></span>
        <span className="w-10 border-t border-dashed border-gray-400"></span>
        <span className="p-1 bg-cstm-blue-700 rounded-full"></span>
        <span className="truncate">{ride.to}</span>
      </div>

      <p className="text-[14px] text-[#76787A] font-normal">
        {ride.date} • {ride.time}
      </p>

      {/* Mobile View: Price + Cancel inline */}
      <div className="flex justify-between items-center pt-1 sm:hidden">
        <p className="font-semibold text-[18px]">{ride.price}</p>
        <button
        onClick={() => {
          if (!isCanceled) setShowModal(true); // Disable reopen if already canceled
        }}
        className={`text-sm px-4 py-1 rounded-full transition 
          ${isCanceled 
            ? "text-red-600 border border-transparent cursor-default" 
            : "border btn-border text-[#76787A] hover:bg-gray-100"}`}
      >
        {isCanceled ? "Canceled" : "Cancel"}
      </button>
      </div>

      {/* Desktop-only price */}
      <p className="font-semibold text-[#050013] text-[20px] hidden sm:block">{ride.price}</p>
    </div>

    {/* Desktop-only Cancel button */}
    <div className="hidden sm:flex items-start">
    <button
        onClick={() => {
          if (!isCanceled) setShowModal(true); // Disable reopen if already canceled
        }}
        className={`text-sm px-4 py-1 rounded-full transition 
          ${isCanceled 
            ? "text-red-600 border border-transparent cursor-default" 
            : "border btn-border text-[#76787A] hover:bg-gray-100"}`}
      >
        {isCanceled ? "Canceled" : "Cancel"}
      </button>
    </div>
  </div>
))}

    </div>
  )} 



  {/* Later Section */}
  {laterRides.length > 0 && (
  <div className="space-y-3">
    <p className="text-[#76787A] text-[14px] font-normal">Later</p>

    {laterRides.map((ride, idx) => (
      <div
        key={idx}
        className="bg-white rounded-xl shadow-[0px_2px_15px_0px_#1D24610D] p-6 mb-6 flex flex-col sm:flex-row justify-between gap-4 items-center"
      >
        {/* Left: Ride details */}
        <div className="space-y-1 w-full">
          <p className="text-[16px] text-purple font-medium">{ride.id}</p>

          <div className="flex items-center gap-2 text-[16px] text-[#050013] font-medium truncate">
            <span>{ride.from}</span>
            <span className="p-1 bg-cstm-blue-700 rounded-full"></span>
            <span className="w-10 border-t border-dashed border-gray-400"></span>
            <span className="p-1 bg-cstm-blue-700 rounded-full"></span>
            <span className="truncate">{ride.to}</span>
          </div>

          <p className="text-[14px] text-[#76787A] font-normal">
            {ride.date} • {ride.time}
          </p>

          {/* Responsive row for Price + Cancel */}
          <div className="flex justify-between items-center pt-1 sm:hidden">
            <p className="font-semibold text-[18px]">{ride.price}</p>
            <button
        onClick={() => {
          if (!isCanceled) setShowModal(true); // Disable reopen if already canceled
        }}
        className={`text-sm px-4 py-1 rounded-full transition 
          ${isCanceled 
            ? "text-red-600 border border-transparent cursor-default" 
            : "border btn-border text-[#76787A] hover:bg-gray-100"}`}
      >
        {isCanceled ? "Canceled" : "Cancel"}
      </button>
          </div>

          {/* Desktop-only price */}
          <p className="font-semibold text-[#050013] text-[20px] hidden sm:block">{ride.price}</p>
        </div>

        {/* Desktop-only cancel button */}
        <div className="hidden sm:flex items-start">
        <button
        onClick={() => {
          if (!isCanceled) setShowModal(true); // Disable reopen if already canceled
        }}
        className={`text-sm px-4 py-1 rounded-full transition 
          ${isCanceled 
            ? "text-red-600 border border-transparent cursor-default" 
            : "border btn-border text-[#76787A] hover:bg-gray-100"}`}
      >
        {isCanceled ? "Canceled" : "Cancel"}
      </button>
        </div>
      </div>
    ))}
  </div>
)}

          </TabPanel>


          {/* Completed Tab Panel */}
          <TabPanel className="space-y-6">
          {todayRides.length > 0 && (
  <div className="space-y-3">
    <p className="text-[#76787A] text-[14px] font-normal">Today</p>

    {todayRides.map((ride, idx) => (
      <div
      key={idx}
      className={`bg-white rounded-xl shadow-[0px_2px_15px_0px_#1D24610D] p-6 mb-6 flex flex-col sm:flex-row justify-between gap-4 items-center ${
        isCanceled ? "opacity-70" : ""
      }`}
    >
        {/* Left: Ride details */}
        <div className="space-y-1">
          <p className="text-[16px] text-purple font-medium">{ride.id}</p>

          <div className="flex items-center gap-2 text-[16px] text-[#050013] font-medium truncate">
            <span>{ride.from}</span>
            <span className="p-1 bg-cstm-blue-700 rounded-full"></span>
            <span className="w-10 border-t border-dashed border-gray-400"></span>
            <span className="p-1 bg-cstm-blue-700 rounded-full"></span>
            <span className="truncate">{ride.to}</span>
          </div>

          <p className="text-[14px] text-[#76787A] font-normal">
            {ride.date} • {ride.time}
          </p>

          {/* Mobile View: Price + Re-Book inline */}
          <div className="flex justify-between items-center pt-1 sm:hidden">
            <p className="font-semibold text-[18px]">{ride.price}</p>
            <button className="border text-[14px] text-[#76787A] px-4 py-1.5 rounded-full hover:bg-gray-100">
              Re-Book
            </button>
          </div>

          {/* Desktop-only price */}
          <p className="font-semibold text-[20px] text-[#050013] hidden sm:block">{ride.price}</p>
        </div>

        {/* Desktop-only Re-Book button */}
        <div className="hidden sm:flex items-start">
          <button className="border text-[14px] text-[#76787A] px-4 py-2 rounded-full hover:bg-gray-100">
            Re-Book
          </button>
        </div>
      </div>
    ))}
  </div>
)}


{todayRides.length > 0 && (
  <div className="space-y-3">
    {todayRides.map((ride, idx) => (
      <div
      key={idx}
      className={`bg-white rounded-xl shadow-[0px_2px_15px_0px_#1D24610D] p-6 mb-6 flex flex-col sm:flex-row justify-between gap-4 items-center ${
        isCanceled ? "opacity-70" : ""
      }`}
    >
        {/* Left: Ride details */}
        <div className="space-y-1">
          <p className="text-[16px] text-purple font-medium">{ride.id}</p>

          <div className="flex items-center gap-2 text-[16px] text-[#050013] font-medium truncate">
            <span>{ride.from}</span>
            <span className="p-1 bg-cstm-blue-700 rounded-full"></span>
            <span className="w-10 border-t border-dashed border-gray-400"></span>
            <span className="p-1 bg-cstm-blue-700 rounded-full"></span>
            <span className="truncate">{ride.to}</span>
          </div>

          <p className="text-[14px] text-[#76787A] font-normal">
            {ride.date} • {ride.time}
          </p>

          {/* Mobile View: Price + Re-Book inline */}
          <div className="flex justify-between items-center pt-1 sm:hidden">
            <p className="font-semibold text-[18px]">{ride.price}</p>
            <button className="border text-[14px] text-[#76787A] px-4 py-1.5 rounded-full hover:bg-gray-100">
              Re-Book
            </button>
          </div>

          {/* Desktop-only price */}
          <p className="font-semibold text-[#050013] text-[20px] hidden sm:block">{ride.price}</p>
        </div>

        {/* Desktop-only Re-Book button */}
        <div className="hidden sm:flex items-start">
          <button className="border text-[14px] text-[#76787A] px-4 py-2 rounded-full hover:bg-gray-100">
            Re-Book
          </button>
        </div>
      </div>
    ))}
  </div>
)}



{todayRides.length > 0 && (
  <div className="space-y-3">
    {todayRides.map((ride, idx) => (
      <div
      key={idx}
      className={`bg-white rounded-xl shadow-[0px_2px_15px_0px_#1D24610D] p-6 mb-6 flex flex-col sm:flex-row justify-between gap-4 items-center ${
        isCanceled ? "opacity-70" : ""
      }`}
    >
        {/* Left: Ride details */}
        <div className="space-y-1">
          <p className="text-[16px] text-purple font-medium">{ride.id}</p>

          <div className="flex items-center gap-2 text-[16px] text-[#050013] font-medium truncate">
            <span>{ride.from}</span>
            <span className="p-1 bg-cstm-blue-700 rounded-full"></span>
            <span className="w-10 border-t border-dashed border-gray-400"></span>
            <span className="p-1 bg-cstm-blue-700 rounded-full"></span>
            <span className="truncate">{ride.to}</span>
          </div>

          <p className="text-[14px] text-[#76787A] font-normal">
            {ride.date} • {ride.time}
          </p>

          {/* Mobile View: Price + Re-Book inline */}
          <div className="flex justify-between items-center pt-1 sm:hidden">
            <p className="font-semibold text-[18px]">{ride.price}</p>
            <button className="border text-[14px] text-[#76787A] px-4 py-1.5 rounded-full hover:bg-gray-100">
              Re-Book
            </button>
          </div>

          {/* Desktop-only price */}
          <p className="font-semibold text-[#050013] text-[20px] hidden sm:block">{ride.price}</p>
        </div>

        {/* Desktop-only Re-Book button */}
        <div className="hidden sm:flex items-start">
          <button className="border text-[14px] text-[#76787A] px-4 py-2 rounded-full hover:bg-gray-100">
            Re-Book
          </button>
        </div>
      </div>
    ))}
  </div>
)}

          </TabPanel>
        </TabPanels>
      </TabGroup>

      {showModal && (
        <div className="fixed inset-0 bg-[#222222]/70 z-999 flex items-center justify-center px-4">
        <div className="bg-white rounded-2xl w-full max-w-sm p-6 shadow-lg text-center">
          {/* Title */}
          <h2 className="text-[18px] font-medium text-[#050013] mb-2">
            Cancel Ride
          </h2>

          {/* Description */}
          <p className="text-[13px] text-[#76787A] mb-6">
            Are you sure you want to cancel this ride? <br />
            A cancellation fee may apply based on the ride policy.
          </p>

          {/* Buttons */}
          <div className="flex justify-center gap-4">
              <button
                onClick={() => setShowModal(false)}
                className="border border-[#4A3AFF] text-[#4A3AFF] font-medium px-6 py-2 rounded-full hover:bg-indigo-50 transition"
              >
                No
              </button>
              <button
                onClick={() => {
                  setIsCanceled(true); // ✅ Change button label
                  setShowModal(false); // ✅ Close modal
                }}
                className="bg-[#4A3AFF] text-white font-medium px-6 py-2 rounded-full hover:bg-[#382fde] transition"
              >
                Yes
              </button>
            </div>
        </div>
        </div>
       )}

    </div>
  );
}
