"use client";

import React, { FC, ReactNode } from "react";
import { useSidebar } from "@/context/SidebarContext";
import AppSidebar from "@/layout/AppSidebar";
import Backdrop from "@/layout/Backdrop";
import { HeaderTitleProvider } from "@/context/HeaderTitleContext";
import PassangerWebFooter from "@/layout/PassangerWebFooter";
import { usePathname } from "next/navigation";
import PassangerHeader from "@/layout/PassangerHeader";
import PassangerSidebar from "@/layout/PassangerSidebar";

interface DriverLayoutProps {
  children: ReactNode;
}

const DriverLayout: FC<DriverLayoutProps> = ({ children }) => {
  const pathname = usePathname();
  const { isExpanded, isHovered, isMobileOpen } = useSidebar();

  const
    mainContentMargin = isMobileOpen
      ? "ml-0"
      : isExpanded || isHovered
        ? "lg:ml-[235px]"
        : "lg:ml-[90px]",
    mainContent = "mx-auto max-w-screen-2xl bg-white mb-[15%] sm:mb-[5%]"
      + ((pathname != "/driver/live" && pathname != "/driver/start-ride") ? " sm:p-4" : '');

  console.log("///////", mainContent);


  return (
    <HeaderTitleProvider>
      <div className="min-h-screen xl:flex">
        <PassangerSidebar />
        <Backdrop />

        {/* Main Content */}
        <div
          className={`flex-1 transition-all duration-300 ease-in-out bg-white ${mainContentMargin}`}
        >
          <PassangerHeader />
          <main className={mainContent}>{children}</main>
          <PassangerWebFooter />
        </div>
      </div>
    </HeaderTitleProvider>


  );
};

export default DriverLayout;