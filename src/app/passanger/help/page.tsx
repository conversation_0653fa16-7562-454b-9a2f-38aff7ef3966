"use client";

import { useEffect, useState } from "react";
import {
  FiPhone, FiMessageSquare, FiChevronRight, FiPlus, FiMinus,
} from "react-icons/fi";
import { LiaCarSideSolid, LiaInboxSolid } from "react-icons/lia";
import { MdOutlinePayment, MdOutlineMail } from "react-icons/md";
import { HiOutlineCurrencyEuro } from "react-icons/hi2";
import { useHeaderTitle } from "@/context/HeaderTitleContext";
import { IoIosSearch } from "react-icons/io";
import { LuMessageSquareMore } from "react-icons/lu";


export default function HelpPage() {
  const { setTitle } = useHeaderTitle();
  const [modalContent, setModalContent] = useState(null);
  const [faqOpenIndex, setFaqOpenIndex] = useState(null);

  useEffect(() => {
    if (setTitle) setTitle("Passenger Help");
  }, [setTitle]);

  const openModal = (content) => setModalContent(content);
  const closeModal = () => setModalContent(null);

  const faqItems = [
    "How do I request a refund",
    "Where can I see my ride history",
    "How to contact my driver",
  ];

  return (
    <div className="relative">
      {/* Main Content */}
      <div className="p-4 sm:p-6 space-y-6">
        <div className="border bg-tables p-6 rounded-xl">
        {/* Search */}
       <div className="relative w-full mb-6">
          <span className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none text-gray-400">
          <IoIosSearch size={22}/>
          </span>
          <input
            type="text"
            placeholder="Search for help"
            className="w-full pl-11 pr-4 py-3 rounded-xl border border-gray text-[14px] focus:outline-none focus:ring-2 focus:ring-indigo-200"
          />
        </div>
        {/* 24/7 Support */}
        <div className="grid grid-cols-2 gap-4">
          <div onClick={() => openModal("Live Chat")} className="cursor-pointer flex flex-col sm:flex-row items-center p-4 bg-white rounded-xl shadow-[0px_2px_15px_0px_#1D24610D] gap-3">
            <div className="bg-purple-100 text-purple-600 p-3 rounded-full">
              <LuMessageSquareMore className="text-sm" />
            </div>
            <p className="font-medium text-[13px] text-[#050013]">24/7 Live Chat</p>
          </div>
          <div onClick={() => openModal("Call Support")} className="cursor-pointer flex flex-col sm:flex-row items-center p-4 bg-white rounded-xl shadow-[0px_2px_15px_0px_#1D24610D] gap-3">
            <div className="bg-purple-100 text-purple-600 p-3 rounded-full">
              <FiPhone className="text-sm" />
            </div>
            <p className="font-medium text-[13px] text-[#050013]">24/7 Call</p>
          </div>
        </div>
        {/* Common Issues */}
        <div>
          <h3 className="text-[13px] text-[#76787A] mt-6 mb-3">Common issues</h3>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <div onClick={() => openModal("Ride Issue")} className="cursor-pointer flex items-center justify-between 
            p-4 bg-white rounded-xl shadow-[0px_2px_15px_0px_#1D24610D]">
              <div className="flex items-center gap-3">
                <div className="bg-red-100 text-red-500 p-3 rounded-full">
                  <LiaCarSideSolid className="text-xl" />
                </div>
                <p className="font-medium text-[13px] text-[#050013]">Ride Issue</p>
              </div>
              <FiChevronRight className="text-gray-400" />
            </div>
            <div onClick={() => openModal("Payment Issue")} className="cursor-pointer flex items-center justify-between p-4 bg-white rounded-xl shadow-[0px_2px_15px_0px_#1D24610D]">
              <div className="flex items-center gap-3">
                <div className="bg-green-100 text-green-500 p-3 rounded-full">
                  <HiOutlineCurrencyEuro className="text-xl" />
                </div>
                <p className="font-medium text-[13px] text-[#050013]">Payment issue</p>
              </div>
              <FiChevronRight className="text-gray-400" />
            </div>
            <div onClick={() => openModal("Lost Items")} className="cursor-pointer flex items-center justify-between p-4 bg-white rounded-xl shadow-[0px_2px_15px_0px_#1D24610D]">
              <div className="flex items-center gap-3">
                <div className="bg-gray-100 text-gray-500 p-3 rounded-full">
                  <LiaInboxSolid className="text-xl" />
                </div>
                <p className="font-medium text-[13px] text-[#050013]">Lost Items</p>
              </div>
              <FiChevronRight className="text-gray-400" />
            </div>
          </div>
        </div>
        </div>
        {/* FAQ */}
        <div className="bg-white rounded-xl border">
          <h3 className="text-[14px] rounded-t-xl font-medium bg-tables text-[#050013] px-5 py-4">FAQ</h3>
          <div className="divide-y divide-gray-200 px-5">
            {faqItems.map((item, index) => (
              <div key={index} className="py-4">
                <div
                  className="flex justify-between items-center cursor-pointer"
                  onClick={() => setFaqOpenIndex(faqOpenIndex === index ? null : index)}
                >
                  <p className="text-[14px] font-normal text-[#050013]">{item}</p>
                  {faqOpenIndex === index ? (
                    <FiMinus size={20} className="text-[#050013]" />
                  ) : (
                    <FiPlus size={20} className="text-purple" />
                  )}
                </div>
                {faqOpenIndex === index && (
                  <div className="mt-2 text-[14px] font-normal text-[#050013] py-5">
                    This is the answer for {item}.
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Slide-in Modal Box (bottom right, fixed width) */}
      <div
        className={`fixed inset-0 z-[999] flex justify-end bg-black/30 transition-opacity duration-300 ease-in-out ${
          modalContent ? "translate-x-0" : "translate-x-full"
        } rounded-l-2xl`}
      >
        <div className="p-6 flex flex-col w-full sm:max-w-md bg-white absolute bottom-0 h-[600px] overflow-y-scroll sm:relative sm:h-full 
          sm:overflow-visible shadow-lg transform transition-all duration-500 translate-x-0 sm:translate-x-0 r rounded-tl-[18px] rounded-tr-[18px] sm:rounded-tr-none 
          flex flex-col justify-between">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-lg font-semibold">{modalContent}</h2>
            <button
              onClick={closeModal}
              className="text-gray-400 hover:text-gray-600 text-2xl"
            >
              &times;
            </button>
          </div>
          <div className="flex-1 text-sm text-gray-600">
            This is the content for <strong>{modalContent}</strong>. Add your details, chat, or help info here.
          </div>
        </div>
      </div>
    </div>
  );
}
