"use client";

import React, { useEffect, useState } from "react";
import { Phone, MapPin, Flag } from "lucide-react";
import { IoMdStar } from "react-icons/io";
import { LuFlagTriangleRight } from "react-icons/lu";
import { FiChevronDown } from "react-icons/fi";

export default function BookRidePage() {
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const checkScreen = () => {
      setIsMobile(window.innerWidth < 640);
    };
    checkScreen();
    window.addEventListener("resize", checkScreen);
    return () => window.removeEventListener("resize", checkScreen);
  }, []);

  return (
    <div className="min-h-screen bg-white flex flex-col md:flex-row gap-4 p-4">
      
      {/* Left: Map */}
      <div className="w-full md:w-1/2 h-[300px] md:h-[90vh] rounded-3xl overflow-hidden shadow">
        <iframe
          title="Map"
          className="w-full h-full"
          src="https://maps.google.com/maps?q=Nieuwe%20Amsterdamseweg&t=&z=15&ie=UTF8&iwloc=&output=embed"
          frameBorder="0"
          allowFullScreen
        />
      </div>

      {/* Right Card - visible only on desktop */}
      <div className="hidden sm:flex w-full md:w-1/2 bg-white rounded-2xl border flex-col justify-between max-h-[600px] h-fit">
        <RideInfo />
      </div>

      {/* Mobile Modal - auto shown */}
      {isMobile && (
        <div className="fixed inset-0 z-999 bg-black/30 flex justify-end sm:hidden">
          <div className="w-full h-[88%] bg-white rounded-t-2xl shadow-lg overflow-y-auto sm:p-6">
            <RideInfo />
          </div>
        </div>
      )}
    </div>
  );
}

// Ride Info Component
const RideInfo = () => (
  <div className="w-full">
      <div className="sm:hidden w-full md:w-1/2 h-[300px] md:h-[90vh] sm:rounded-3xl overflow-hidden shadow">
        <iframe
          title="Map"
          className="w-full h-full"
          src="https://maps.google.com/maps?q=Nieuwe%20Amsterdamseweg&t=&z=15&ie=UTF8&iwloc=&output=embed"
          frameBorder="0"
          allowFullScreen
        />
      </div>
    {/* Top Banner */}
    <div className="bg-[#050013] text-white text-center pt-4 pb-6 rounded-2xl text-sm rounded-b-none font-medium">
      Your Driver will Arrive on 15 Feb 2025, 10:30AM
    </div>
    <div className="bg-white -mt-2 rounded-2xl rounded-t-[12px]">
    {/* Driver Info */}
    <div className="flex items-start gap-4 mb-6 border-b p-6">
      <img
        src="/images/user.jpg"
        alt="Driver"
        className="w-12 h-12 rounded-full object-cover"
      />
      <div>
        <h3 className="text-[16px] font-medium text-[#050013] flex items-center gap-2">
          Rivka Frank <span className="text-[#050013] flex items-center gap-1 size={12}"><IoMdStar size={18} className="text-[#FF9601]"/>4.5</span>
        </h3>
        <p className="text-[13px] text-[#76787A]">Toyota Camry · Black</p>
        <p className="text-[14px] mt-1 text-[#050013] font-semibold">NL-00–12568</p>
      </div>
      <button className="ml-auto border-btn p-3 rounded-full">
        <Phone size={18} className="text-purple" />
      </button>
    </div>

    {/* Pickup & Drop Info */}
    <div className="space-y-6 mb-6 px-6 relative">
  {/* Line between icons */}
  <div className="absolute left-[7.3%] sm:left-[6.3%] h-[40%] top-6 bottom-6 border-l border-dashed border-gray-300 z-0"></div>

  {/* Pick up */}
  <div className="flex gap-3 items-start relative z-10">
    <span className="bg-blue-50 p-1 rounded-full z-10">
      <MapPin className="text-purple" size={16} />
    </span>
    <div>
      <p className="text-[12px] text-[#76787A]">Pick up</p>
      <p className="text-[14px] font-normal text-[#050013]">288–292, Spuistraat City</p>
    </div>
  </div>

  {/* Drop off */}
  <div className="flex gap-3 items-start relative z-10">
    <span className="bg-blue-50 p-1 rounded-full z-10">
      <LuFlagTriangleRight className="text-purple" size={16} />
    </span>
    <div>
      <p className="text-[12px] text-[#76787A]">Drop off</p>
      <p className="text-[14px] font-normal text-[#050013]">1000 AP, Schipol Airport</p>
    </div>
  </div>
</div>

    <div className="px-6 pb-6">
    {/* Fare Estimate */}
    <div className="flex justify-between items-center bg-tables px-4 py-3 rounded-xl mb-6">
      {/* Label + Icon + Dropdown */}
      <div className="flex justify-between items-center mb-2">
        <div className="relative w-[100%]">
          {/* Right Chevron */}
          <span className="absolute rounded-full p-[2px] right-3 top-1/2 transform -translate-y-1/2 text-[#050013]
          pointer-events-none text-lg bg-white">
          <FiChevronDown /></span>

          {/* Select Box */}
          <select
            id="fareType"
           
            className="appearance-none text-[14px] text-[#76787A] pr-10 focus:outline-none focus:ring-2 focus:ring-[#3B00F3]">
            <option>Fare Estimate</option>
            <option>Premium</option>
            <option>Shared</option>
          </select>
        </div>
      </div>

      {/* Right Price */}
      <span className="text-[18px] font-medium">€ 35.00</span>
    </div>


    {/* Cancel Ride Button */}
    <button className="btn-border rounded-full py-3 text-center text-[14px] text-[#76787A] font-semibold hover:bg-gray-100 transition w-full">
      Cancel Ride
    </button>
    </div>
    </div>

  </div>
);
