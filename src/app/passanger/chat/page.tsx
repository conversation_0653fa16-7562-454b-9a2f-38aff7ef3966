"use client";

import { useState, useEffect } from "react";
import { useHeaderTitle } from "@/context/HeaderTitleContext";
import { FiPhone, FiX, FiCamera, FiMic } from "react-icons/fi";
import { RxChevronLeft } from "react-icons/rx";
import { FaCircle, FaPaperPlane } from "react-icons/fa";
import { LuClock3 } from "react-icons/lu";


export default function ChatPage() {
  const { setTitle } = useHeaderTitle();
  const [message, setMessage] = useState("");
  const [selectedQuick, setSelectedQuick] = useState(null);
  const [popupOpened, setPopupOpened] = useState<boolean>(false);

  useEffect(() => {
    setTitle("Chat");
  }, [setTitle]);

  const messages = [
    {
      type: "received",
      text: "Hi Rivka! Where are you parked",
      time: "9:15 AM",
      avatar: "/user1.png",
    },
    {
      type: "sent",
      text: "Hi <PERSON>! I'm on my way to pick you up at Spuistraat City Centre, ETA: 5 minutes.",
      time: "9:16 AM",
      avatar: "/user2.png",
    },
    {
      type: "info",
      text: "Waiting time: 5 min",
    },
  ];

  const quickReplies = [
    "I'll be there in 2 mins",
    "Traffic delay",
    "I have arrived",
    "Can't find you",
  ];

  const handleQuickClick = (text) => {
    setMessage(text);
    setSelectedQuick(text);
  };

  return (
    <div className="flex flex-col h-[calc(100vh-80px)] border bg-white rounded-2xl">
      {/* Header */}
      <div className="flex items-center justify-between p-6 border-b">
        <div className="flex items-start">
        <RxChevronLeft className="m-[5px] text-[#76787A]" size={20}/>
          <div>
            <p className="font-semibold text-[18px] text-[#050013]">Rivka Frank</p>
            <div className="flex items-center gap-4">
            <p className="text-[13px] text-[#76787A] font-normal">Driver</p>
            <span className="text-[13px] text-green-600 flex items-center gap-1"><FaCircle size={10}/>Online</span>
            </div>
          </div>
        </div>
        <span className="p-3 rounded-full border-btn"><FiPhone className="text-purple text-xl" /></span>
      </div>

      {/* Chat body */}
      <div className="flex-1 overflow-y-auto space-y-4 p-6">
        <div className="flex justify-center">
          <span className="bg-[#F4F5F7] text-[#050013] text-xs px-5 py-2 rounded-full">Today</span>
        </div>

        {messages.map((msg, i) => (
          <div key={i} className={`flex ${(msg.type === "sent") ? "justify-end" : "justify-start"}`}>
            {msg.type === "received" && (
              <div className="flex gap-2">
                <img src="/images/avatar.jpg" alt="avatar" className="w-8 h-8 rounded-full" />
                <div>
                  <div className="bg-[#F4F5F7] text-[13px] text-[#050013] px-5 py-2 rounded-full max-w-[280px] rounded-tl-none">
                    {msg.text}
                  </div>
                  <p className="text-[12px] text-[#76787A] mt-1">{msg.time}</p>
                </div>
              </div>
            )}

            {msg.type === "sent" && (
              <div className="flex flex-col">
                <div className="flex gap-2 items-start">
                  <div>
                    <div className="bg-gradient-to-r from-indigo-600 to-teal-400 text-white text-[13px] px-5 py-2 rounded-xl rounded-tr-none max-w-[350px]">
                      {msg.text}
                    </div>
                    <p className="text-[12px] text-[#76787A] text-left mt-1">{msg.time}</p>
                  </div>
                  <img src="/images/user2.jpg" alt="avatar" className="w-8 h-8 rounded-full" />
                </div>
                <div className="w-fit mt-4 flex items-center gap-2 bg-[#FFF4E5] text-[#050013] text-[12px] px-4 py-2 rounded-full">
                <span><LuClock3 className="text-[#C3801B]" size={18}/>
                </span>
                <span>Waiting time: 5 min</span>
              </div>
              </div>
            )}
          </div>
        ))}
      </div>

      {/* Quick Replies */}
      <div className="flex flex-wrap gap-2 p-6">
        {quickReplies.map((q, i) => (
          <button
            key={i}
            onClick={() => handleQuickClick(q)}
            className={`border px-4 py-1.5 rounded-full text-sm hover:bg-gray-100 ${
              selectedQuick === q ? "border-btn text-purple bg-blue-50" : "border-gray-300"
            }`}
          >
            {q}
          </button>
        ))}
      </div>

      {/* Message Input */}
      <div className="px-6 pb-6">
      <div className="flex items-center border rounded-full px-4 py-2">
        <input
          type="text"
          placeholder="Type your message..."
          className="flex-1 text-[13px] focus:outline-none focus:ring-0 focus:!border-0"
          value={message}
          onChange={(e) => setMessage(e.target.value)}
        />
        <button className="text-purple p-2 rounded-full !hover:bg-table">
          <FaPaperPlane className="text-lg" />
        </button>
      </div>
      </div>

      <div className="sm:hidden fixed inset-0 z-[999] flex justify-end bg-black/30 transition-opacity duration-300 ease-in-out">
        <div className="w-full sm:max-w-md bg-white absolute bottom-0 h-[600px] overflow-y-scroll sm:relative sm:h-full 
        sm:overflow-visible shadow-lg transform transition-all duration-500 translate-x-0 sm:translate-x-0 r rounded-tl-[18px] rounded-tr-[18px] sm:rounded-tr-none 
        flex flex-col justify-between">
          <div className="flex items-center justify-between p-6 border-b">
            <div className="flex items-start">
            <RxChevronLeft className="m-[5px] text-[#76787A]" size={20}/>
              <div>
                <p className="font-semibold text-[18px] text-[#050013]">Rivka Frank</p>
                <div className="flex items-center gap-4">
                <p className="text-[13px] text-[#76787A] font-normal">Driver</p>
                <span className="text-[13px] text-green-600 flex items-center gap-1"><FaCircle size={10}/>Online</span>
                </div>
              </div>
            </div>
            <span className="p-2 rounded-full border-btn"><FiPhone size={15} className="text-purple text-xl" /></span>
          </div>

          {/* Chat body */}
          <div className="flex-1 overflow-y-auto space-y-4 p-6">
            <div className="flex justify-center">
              <span className="bg-[#F4F5F7] text-[#050013] text-xs px-5 py-2 rounded-full">Today</span>
            </div>

            {messages.map((msg, i) => (
              <div key={i} className={`flex ${(msg.type === "sent") ? "justify-end" : "justify-start"}`}>
                {msg.type === "received" && (
                  <div className="flex gap-2">
                    <img src="/images/avatar.jpg" alt="avatar" className="w-8 h-8 rounded-full" />
                    <div>
                      <div className="bg-[#F4F5F7] text-[13px] text-[#050013] px-5 py-2 rounded-full max-w-[280px] rounded-tl-none">
                        {msg.text}
                      </div>
                      <p className="text-[12px] text-[#76787A] mt-1">{msg.time}</p>
                    </div>
                  </div>
                )}

                {msg.type === "sent" && (
                  <div className="flex flex-col">
                    <div className="flex gap-2 items-start">
                      <div>
                        <div className="bg-gradient-to-r from-indigo-600 to-teal-400 text-white text-[13px] px-5 py-2 rounded-xl rounded-tr-none max-w-[350px]">
                          {msg.text}
                        </div>
                        <p className="text-[12px] text-[#76787A] text-left mt-1">{msg.time}</p>
                      </div>
                      <img src="/images/user2.jpg" alt="avatar" className="w-8 h-8 rounded-full" />
                    </div>
                    <div className="w-fit mt-4 flex items-center gap-2 bg-[#FFF4E5] text-[#050013] text-[12px] px-4 py-2 rounded-full">
                    <span><LuClock3 className="text-[#C3801B]" size={18}/>
                    </span>
                    <span>Waiting time: 5 min</span>
                  </div>
                  </div>
                )}
              </div>
            ))}
          </div>

          {/* Quick Replies */}
          <div className="flex flex-wrap gap-2 p-6">
            {quickReplies.map((q, i) => (
              <button
                key={i}
                onClick={() => handleQuickClick(q)}
                className={`border px-4 py-1.5 rounded-full text-sm hover:bg-gray-100 ${
                  selectedQuick === q ? "border-btn text-purple bg-blue-50" : "border-gray-300"
                }`}
              >
                {q}
              </button>
            ))}
          </div>

          {/* Message Input */}
          <div className="px-6 pb-6">
          <div className="flex items-center border rounded-full px-4 py-2">
            <input
              type="text"
              placeholder="Type your message..."
              className="flex-1 text-[13px] focus:outline-none focus:ring-0 focus:!border-0"
              value={message}
              onChange={(e) => setMessage(e.target.value)}
            />
            <button className="text-purple p-2 rounded-full !hover:bg-table" onClick={() => setPopupOpened(true)}>
              <FaPaperPlane className="text-lg" />
            </button>
          </div>
          </div>
        </div>
      </div>
    </div>
  );
}